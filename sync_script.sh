#!/bin/bash

# BambuStudio 代码同步脚本
# 用于管理本地代码与远程仓库的同步

# 配置
REPO_URL="https://github.com/wsd07/BambuStudio.git"
UPSTREAM_URL="https://github.com/bambulab/BambuStudio.git"
LOCAL_DIR="BambuStudio"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在正确的目录
check_directory() {
    if [ ! -d "$LOCAL_DIR" ]; then
        log_error "BambuStudio 目录不存在！请先克隆仓库。"
        exit 1
    fi
    cd "$LOCAL_DIR"
}

# 显示帮助信息
show_help() {
    echo "BambuStudio 代码同步脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  status          显示当前状态"
    echo "  pull            从远程仓库拉取最新代码"
    echo "  push            推送本地代码到远程仓库"
    echo "  sync            同步上游仓库的最新代码"
    echo "  branch [name]   创建并切换到新分支"
    echo "  list-branches   列出所有分支"
    echo "  switch [name]   切换分支"
    echo "  commit [msg]    提交当前更改"
    echo "  setup           设置远程仓库"
    echo "  clean           清理工作目录"
    echo "  backup          备份当前工作"
    echo "  help            显示此帮助信息"
    echo ""
}

# 显示状态
show_status() {
    log_info "检查仓库状态..."
    check_directory
    
    echo "=== Git 状态 ==="
    git status --short
    
    echo ""
    echo "=== 当前分支 ==="
    git branch --show-current
    
    echo ""
    echo "=== 远程仓库 ==="
    git remote -v
    
    echo ""
    echo "=== 最近提交 ==="
    git log --oneline -5
}

# 拉取代码
pull_code() {
    log_info "从远程仓库拉取最新代码..."
    check_directory
    
    current_branch=$(git branch --show-current)
    log_info "当前分支: $current_branch"
    
    git fetch origin
    git pull origin "$current_branch"
    
    if [ $? -eq 0 ]; then
        log_success "代码拉取成功！"
    else
        log_error "代码拉取失败！"
        exit 1
    fi
}

# 推送代码
push_code() {
    log_info "推送本地代码到远程仓库..."
    check_directory
    
    current_branch=$(git branch --show-current)
    log_info "当前分支: $current_branch"
    
    # 检查是否有未提交的更改
    if [ -n "$(git status --porcelain)" ]; then
        log_warning "检测到未提交的更改，请先提交或暂存。"
        git status --short
        return 1
    fi
    
    git push origin "$current_branch"
    
    if [ $? -eq 0 ]; then
        log_success "代码推送成功！"
    else
        log_error "代码推送失败！"
        exit 1
    fi
}

# 同步上游仓库
sync_upstream() {
    log_info "同步上游仓库的最新代码..."
    check_directory
    
    # 检查是否设置了上游仓库
    if ! git remote | grep -q "upstream"; then
        log_info "添加上游仓库..."
        git remote add upstream "$UPSTREAM_URL"
    fi
    
    log_info "获取上游仓库最新代码..."
    git fetch upstream
    
    current_branch=$(git branch --show-current)
    log_info "合并上游代码到当前分支: $current_branch"
    
    git merge upstream/master
    
    if [ $? -eq 0 ]; then
        log_success "上游代码同步成功！"
        log_info "建议推送到远程仓库: ./sync_script.sh push"
    else
        log_error "上游代码同步失败！可能存在冲突，请手动解决。"
        exit 1
    fi
}

# 创建分支
create_branch() {
    if [ -z "$1" ]; then
        log_error "请提供分支名称！"
        echo "用法: $0 branch <分支名称>"
        exit 1
    fi
    
    check_directory
    branch_name="$1"
    
    log_info "创建并切换到分支: $branch_name"
    git checkout -b "$branch_name"
    
    if [ $? -eq 0 ]; then
        log_success "分支 '$branch_name' 创建成功！"
    else
        log_error "分支创建失败！"
        exit 1
    fi
}

# 列出分支
list_branches() {
    check_directory
    log_info "所有分支:"
    git branch -a
}

# 切换分支
switch_branch() {
    if [ -z "$1" ]; then
        log_error "请提供分支名称！"
        echo "用法: $0 switch <分支名称>"
        exit 1
    fi
    
    check_directory
    branch_name="$1"
    
    log_info "切换到分支: $branch_name"
    git checkout "$branch_name"
    
    if [ $? -eq 0 ]; then
        log_success "已切换到分支 '$branch_name'"
    else
        log_error "分支切换失败！"
        exit 1
    fi
}

# 提交更改
commit_changes() {
    if [ -z "$1" ]; then
        log_error "请提供提交信息！"
        echo "用法: $0 commit \"<提交信息>\""
        exit 1
    fi
    
    check_directory
    commit_msg="$1"
    
    # 检查是否有更改
    if [ -z "$(git status --porcelain)" ]; then
        log_warning "没有检测到更改，无需提交。"
        return 0
    fi
    
    log_info "添加所有更改..."
    git add .
    
    log_info "提交更改: $commit_msg"
    git commit -m "$commit_msg"
    
    if [ $? -eq 0 ]; then
        log_success "更改提交成功！"
        log_info "建议推送到远程仓库: ./sync_script.sh push"
    else
        log_error "提交失败！"
        exit 1
    fi
}

# 设置远程仓库
setup_remotes() {
    check_directory
    log_info "设置远程仓库..."
    
    # 检查并设置 origin
    if git remote | grep -q "origin"; then
        log_info "origin 已存在，更新 URL..."
        git remote set-url origin "$REPO_URL"
    else
        log_info "添加 origin..."
        git remote add origin "$REPO_URL"
    fi
    
    # 检查并设置 upstream
    if git remote | grep -q "upstream"; then
        log_info "upstream 已存在，更新 URL..."
        git remote set-url upstream "$UPSTREAM_URL"
    else
        log_info "添加 upstream..."
        git remote add upstream "$UPSTREAM_URL"
    fi
    
    log_success "远程仓库设置完成！"
    git remote -v
}

# 清理工作目录
clean_workspace() {
    check_directory
    log_warning "这将清理所有未跟踪的文件和目录！"
    read -p "确定要继续吗？(y/N): " confirm
    
    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
        log_info "清理工作目录..."
        git clean -fd
        log_success "工作目录清理完成！"
    else
        log_info "操作已取消。"
    fi
}

# 备份当前工作
backup_work() {
    check_directory
    timestamp=$(date +"%Y%m%d_%H%M%S")
    backup_name="backup_$timestamp"
    
    log_info "创建备份分支: $backup_name"
    git checkout -b "$backup_name"
    
    # 如果有未提交的更改，先提交
    if [ -n "$(git status --porcelain)" ]; then
        git add .
        git commit -m "Backup: $timestamp"
    fi
    
    git checkout -
    log_success "备份创建完成: $backup_name"
}

# 主函数
main() {
    case "$1" in
        "status")
            show_status
            ;;
        "pull")
            pull_code
            ;;
        "push")
            push_code
            ;;
        "sync")
            sync_upstream
            ;;
        "branch")
            create_branch "$2"
            ;;
        "list-branches")
            list_branches
            ;;
        "switch")
            switch_branch "$2"
            ;;
        "commit")
            commit_changes "$2"
            ;;
        "setup")
            setup_remotes
            ;;
        "clean")
            clean_workspace
            ;;
        "backup")
            backup_work
            ;;
        "help"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
