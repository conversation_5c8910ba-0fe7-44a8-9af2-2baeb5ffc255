/*
 * Copyright (c) 2003 <PERSON> <comac AT comac DOT darktech DOT org>
 * Copyright (c) 2010 <PERSON>
 * Copyright (c) 2012 <PERSON><PERSON>
 *
 * This file is part of FFmpeg, ported from MPlayer.
 *
 * FFmpeg is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * FFmpeg is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License along
 * with FFmpeg; if not, write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.
 */

#ifndef AVFILTER_HQDN3D_H
#define AVFILTER_HQDN3D_H

#include <stddef.h>
#include <stdint.h>

#include "libavutil/opt.h"

typedef struct HQDN3DContext {
    const AVClass *class;
    int16_t *coefs[4];
    uint16_t *line[3];
    uint16_t *frame_prev[3];
    double strength[4];
    int hsub, vsub;
    int depth;
    void (*denoise_row[17])(uint8_t *src, uint8_t *dst, uint16_t *line_ant, uint16_t *frame_ant, ptrdiff_t w, int16_t *spatial, int16_t *temporal);
} HQDN3DContext;

#define LUMA_SPATIAL   0
#define LUMA_TMP       1
#define CHROMA_SPATIAL 2
#define CHROMA_TMP     3

void ff_hqdn3d_init_x86(HQDN3DContext *hqdn3d);

#endif /* AVFILTER_HQDN3D_H */
