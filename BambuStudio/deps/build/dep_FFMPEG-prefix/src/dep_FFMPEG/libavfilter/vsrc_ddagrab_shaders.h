/*
 * This file is part of FFmpeg.
 *
 * FFmpeg is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * FFmpeg is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with FFmpeg; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

#ifndef AVFILTER_VSRC_DDAGRAB_SHADERS_H
#define AVFILTER_VSRC_DDAGRAB_SHADERS_H

#include <stdint.h>

#if 0

cbuffer PARAMS : register ( b0 )
{
    float2 Dimensions;
};

struct VS_INPUT
{
    float3 Pos : POSITION;
    float2 Tex : TEXCOORD;
};

struct VS_OUTPUT
{
    float4 Pos : SV_POSITION;
    float2 Tex : TEXCOORD;
};

VS_OUTPUT VS(VS_INPUT input)
{
    VS_OUTPUT output;
    float2 center = Dimensions / 2;
    output.Pos = float4((input.Pos.xy - center) / center, input.Pos.z, 1.0f);
    output.Pos.y *= -1;
    output.Tex = input.Tex;
    return output;
}

Texture2D tx : register( t0 );
SamplerState samLinear : register( s0 );

float4 PS(VS_OUTPUT input) : SV_Target
{
    return tx.Sample(samLinear, input.Tex);
}

#endif

static const uint8_t vertex_shader_bytes[] =
{
     68,  88,  66,  67, 207, 194, 142, 193, 255,  85,  32,  72, 116,  77, 242, 140,  26, 229,  67,  69,   1,   0,   0,   0,
     40,   3,   0,   0,   4,   0,   0,   0,  48,   0,   0,   0,  56,   1,   0,   0, 124,   2,   0,   0, 208,   2,   0,   0,
     65, 111, 110,  57,   0,   1,   0,   0,   0,   1,   0,   0,   0,   2, 254, 255, 204,   0,   0,   0,  52,   0,   0,   0,
      1,   0,  36,   0,   0,   0,  48,   0,   0,   0,  48,   0,   0,   0,  36,   0,   1,   0,  48,   0,   0,   0,   0,   0,
      1,   0,   1,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   2, 254, 255,  81,   0,   0,   5,   2,   0,  15, 160,
      0,   0,   0,  63,   0,   0, 128,  63,   0,   0,   0,   0,   0,   0,   0,   0,  31,   0,   0,   2,   5,   0,   0, 128,
      0,   0,  15, 144,  31,   0,   0,   2,   5,   0,   1, 128,   1,   0,  15, 144,   1,   0,   0,   2,   0,   0,   1, 128,
      2,   0,   0, 160,   4,   0,   0,   4,   0,   0,   6, 128,   1,   0, 208, 160,   0,   0,   0, 129,   0,   0, 208, 144,
      5,   0,   0,   3,   0,   0,   9, 128,   0,   0,   0, 128,   1,   0, 100, 160,   6,   0,   0,   2,   1,   0,   1, 128,
      0,   0,   0, 128,   6,   0,   0,   2,   1,   0,   2, 128,   0,   0, 255, 128,   5,   0,   0,   3,   0,   0,   3, 128,
      0,   0, 233, 128,   1,   0, 228, 128,   1,   0,   0,   2,   0,   0,   4, 128,   0,   0,  85, 129,   2,   0,   0,   3,
      0,   0,   3, 192,   0,   0, 232, 128,   0,   0, 228, 160,   4,   0,   0,   4,   0,   0,  12, 192,   0,   0, 170, 144,
      2,   0, 148, 160,   2,   0, 100, 160,   1,   0,   0,   2,   0,   0,   3, 224,   1,   0, 228, 144, 255, 255,   0,   0,
     83,  72,  68,  82,  60,   1,   0,   0,  64,   0,   1,   0,  79,   0,   0,   0,  89,   0,   0,   4,  70, 142,  32,   0,
      0,   0,   0,   0,   1,   0,   0,   0,  95,   0,   0,   3, 114,  16,  16,   0,   0,   0,   0,   0,  95,   0,   0,   3,
     50,  16,  16,   0,   1,   0,   0,   0, 103,   0,   0,   4, 242,  32,  16,   0,   0,   0,   0,   0,   1,   0,   0,   0,
    101,   0,   0,   3,  50,  32,  16,   0,   1,   0,   0,   0, 104,   0,   0,   2,   1,   0,   0,   0,  56,   0,   0,  11,
     50,   0,  16,   0,   0,   0,   0,   0,  70, 128,  32,   0,   0,   0,   0,   0,   0,   0,   0,   0,   2,  64,   0,   0,
      0,   0,   0,  63,   0,   0,   0,  63,   0,   0,   0,   0,   0,   0,   0,   0,  50,   0,   0,  14, 194,   0,  16,   0,
      0,   0,   0,   0,   6, 132,  32, 128,  65,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   2,  64,   0,   0,
      0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,  63,   0,   0,   0,  63,   6,  20,  16,   0,   0,   0,   0,   0,
     14,   0,   0,   7,  50,   0,  16,   0,   0,   0,   0,   0, 230,  10,  16,   0,   0,   0,   0,   0,  70,   0,  16,   0,
      0,   0,   0,   0,  56,   0,   0,  10,  50,  32,  16,   0,   0,   0,   0,   0,  70,   0,  16,   0,   0,   0,   0,   0,
      2,  64,   0,   0,   0,   0, 128,  63,   0,   0, 128, 191,   0,   0,   0,   0,   0,   0,   0,   0,  54,   0,   0,   5,
     66,  32,  16,   0,   0,   0,   0,   0,  42,  16,  16,   0,   0,   0,   0,   0,  54,   0,   0,   5, 130,  32,  16,   0,
      0,   0,   0,   0,   1,  64,   0,   0,   0,   0, 128,  63,  54,   0,   0,   5,  50,  32,  16,   0,   1,   0,   0,   0,
     70,  16,  16,   0,   1,   0,   0,   0,  62,   0,   0,   1,  73,  83,  71,  78,  76,   0,   0,   0,   2,   0,   0,   0,
      8,   0,   0,   0,  56,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   3,   0,   0,   0,   0,   0,   0,   0,
      7,   7,   0,   0,  65,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   3,   0,   0,   0,   1,   0,   0,   0,
      3,   3,   0,   0,  80,  79,  83,  73,  84,  73,  79,  78,   0,  84,  69,  88,  67,  79,  79,  82,  68,   0, 171, 171,
     79,  83,  71,  78,  80,   0,   0,   0,   2,   0,   0,   0,   8,   0,   0,   0,  56,   0,   0,   0,   0,   0,   0,   0,
      1,   0,   0,   0,   3,   0,   0,   0,   0,   0,   0,   0,  15,   0,   0,   0,  68,   0,   0,   0,   0,   0,   0,   0,
      0,   0,   0,   0,   3,   0,   0,   0,   1,   0,   0,   0,   3,  12,   0,   0,  83,  86,  95,  80,  79,  83,  73,  84,
     73,  79,  78,   0,  84,  69,  88,  67,  79,  79,  82,  68,   0, 171, 171, 171
};

static const uint8_t pixel_shader_bytes[] =
{
     68,  88,  66,  67,   0,  95,  83, 169,  90,  60, 208,  75, 219, 179, 108, 203,   8, 232, 255,  27,   1,   0,   0,   0,
    148,   1,   0,   0,   4,   0,   0,   0,  48,   0,   0,   0, 156,   0,   0,   0,   8,   1,   0,   0,  96,   1,   0,   0,
     65, 111, 110,  57, 100,   0,   0,   0, 100,   0,   0,   0,   0,   2, 255, 255,  60,   0,   0,   0,  40,   0,   0,   0,
      0,   0,  40,   0,   0,   0,  40,   0,   0,   0,  40,   0,   1,   0,  36,   0,   0,   0,  40,   0,   0,   0,   0,   0,
      0,   2, 255, 255,  31,   0,   0,   2,   0,   0,   0, 128,   0,   0,   3, 176,  31,   0,   0,   2,   0,   0,   0, 144,
      0,   8,  15, 160,  66,   0,   0,   3,   0,   0,  15, 128,   0,   0, 228, 176,   0,   8, 228, 160,   1,   0,   0,   2,
      0,   8,  15, 128,   0,   0, 228, 128, 255, 255,   0,   0,  83,  72,  68,  82, 100,   0,   0,   0,  64,   0,   0,   0,
     25,   0,   0,   0,  90,   0,   0,   3,   0,  96,  16,   0,   0,   0,   0,   0,  88,  24,   0,   4,   0, 112,  16,   0,
      0,   0,   0,   0,  85,  85,   0,   0,  98,  16,   0,   3,  50,  16,  16,   0,   1,   0,   0,   0, 101,   0,   0,   3,
    242,  32,  16,   0,   0,   0,   0,   0,  69,   0,   0,   9, 242,  32,  16,   0,   0,   0,   0,   0,  70,  16,  16,   0,
      1,   0,   0,   0,  70, 126,  16,   0,   0,   0,   0,   0,   0,  96,  16,   0,   0,   0,   0,   0,  62,   0,   0,   1,
     73,  83,  71,  78,  80,   0,   0,   0,   2,   0,   0,   0,   8,   0,   0,   0,  56,   0,   0,   0,   0,   0,   0,   0,
      1,   0,   0,   0,   3,   0,   0,   0,   0,   0,   0,   0,  15,   0,   0,   0,  68,   0,   0,   0,   0,   0,   0,   0,
      0,   0,   0,   0,   3,   0,   0,   0,   1,   0,   0,   0,   3,   3,   0,   0,  83,  86,  95,  80,  79,  83,  73,  84,
     73,  79,  78,   0,  84,  69,  88,  67,  79,  79,  82,  68,   0, 171, 171, 171,  79,  83,  71,  78,  44,   0,   0,   0,
      1,   0,   0,   0,   8,   0,   0,   0,  32,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   3,   0,   0,   0,
      0,   0,   0,   0,  15,   0,   0,   0,  83,  86,  95,  84,  97, 114, 103, 101, 116,   0, 171, 171
};

#endif
