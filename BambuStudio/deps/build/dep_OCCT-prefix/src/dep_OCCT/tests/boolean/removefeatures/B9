restore [locate_data_file bug29481_ex1.brep] s
explode s f

# removal of the cylindrical spike on the shape

removefeatures result s s_15 s_21 s_22
checkshape result
checkprops result -s 449.707 -v 186.844 -deps 1.e-7
checknbshapes result -vertex 30 -edge 48 -wire 20 -face 18 -shell 1 -solid 1
CheckIsFeatureRemoved s_15 {v e f}
CheckIsFeatureRemoved s_21 {v e f}
CheckIsFeatureRemoved s_22 {v e f}

# get history of the operation
savehistory rf_hist

# check modification of the top face
modified m1 rf_hist s_1
checkprops m1 -s 175.593
checknbshapes m1 -vertex 12 -edge 12 -wire 2 -face 1

# check that no new intersections have been created
if {![regexp "No shapes were generated" [generated g1 rf_hist s_1]]} {
  puts "Error: incorrect feature removal"
}

checkview -display result -2d -path ${imagedir}/${test_image}.png
