# Original bug : pro7637 (#2 with restore)
# Date : 16apr97

dset SCALE 100

## restore the Prismed oblong (size 200 mm)
restore [locate_data_file CTO900_pro7637_prism_oblong.rle] p1
tscale p1 0 0 0 1*SCALE

## Box creation (size 300 mm)
restore [locate_data_file CTO900_pro7637_box.rle] p2
tscale p2 0 0 0 1*SCALE

bfuse result p2 p1

checkprops result -s 1.85425e+09
checkview -display result -2d -otherwise { p2 p1 } -s -path ${imagedir}/${test_image}.png