restore [locate_data_file bug29481_ex1.brep] s
explode s f

# blend removal
removefeatures result s s_15
checkshape result
checkprops result -s 462.273 -v 193.127 -deps 1.e-7
checknbshapes result -vertex 33 -edge 51 -wire 23 -face 20 -shell 1 -solid 1 -t
CheckIsFeatureRemoved s_15 {e f}

# get history of the operation
savehistory rf_hist

# check modification of the top face
modified m1 rf_hist s_1
checkprops m1 -s 172.452
checknbshapes m1 -vertex 13 -edge 13 -wire 3 -face 1

generated g1 rf_hist s_1
checknbshapes g1 -vertex 1 -edge 1
checkprops g1 -l 6.28319

# check modification of the cylindrical face
modified m21 rf_hist s_21
checkprops m21 -s 12.5664
checknbshapes m21 -vertex 2 -edge 3 -wire 1 -face 1

generated g21 rf_hist s_21
checknbshapes g21 -vertex 1 -edge 1
checkprops g21 -equal g1

checkview -display result -2d -path ${imagedir}/${test_image}.png
