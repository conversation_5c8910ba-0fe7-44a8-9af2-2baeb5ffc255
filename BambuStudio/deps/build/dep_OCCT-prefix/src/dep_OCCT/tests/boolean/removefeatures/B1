restore [locate_data_file bug29481_ex1.brep] s
explode s f

# fillet removal
removefeatures result s s_13
checkshape result
checkprops result -s 463.068 -v 194.214 -deps 1.e-7
checknbshapes result -vertex 31 -edge 50 -wire 23 -face 20 -shell 1 -solid 1
CheckIsFeatureRemoved s_13 {v e f}

# get history of the operation
savehistory rf_hist

# check modification of the side faces
modified m2 rf_hist s_2
checkprops m2 -s 20
checknbshapes m2 -vertex 4 -edge 4 -wire 1 -face 1

modified m12 rf_hist s_12
checkprops m12 -s 8
checknbshapes m12 -vertex 4 -edge 4 -wire 1 -face 1

modified m17 rf_hist s_17
checkprops m17 -s 10
checknbshapes m17 -vertex 4 -edge 4 -wire 1 -face 1

generated g17 rf_hist s_17
checkprops g17 -l 12

explode s_17 e
modified m17_1 rf_hist s_17_1
checkprops m17_1 -l 1
modified m17_3 rf_hist s_17_3
checkprops m17_3 -l 1

# check modification of the top face
modified m1 rf_hist s_1
checkprops m1 -s 172.551

checkview -display result -2d -path ${imagedir}/${test_image}.png
