%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Ipelib 60023 (Ipe 6.0 preview 23)
%%CreationDate: D:20050330173107
%%LanguageLevel: 2
%%BoundingBox: -156 -44 62 90
%%HiResBoundingBox: -156.764 -44.2787 61.3997 89.856
%%DocumentSuppliedResources: font GKTLJX+CMR10
%%+ font JPAIIC+CMMI10
%%EndComments
%%BeginProlog
%%BeginResource: procset ipe 6.0 60023
/ipe 40 dict def ipe begin
/np { newpath } def
/m { moveto } def
/l { lineto } def
/c { curveto } def
/h { closepath } def
/re { 4 2 roll moveto 1 index 0 rlineto 0 exch rlineto
      neg 0 rlineto closepath } def
/d { setdash } def
/w { setlinewidth } def
/J { setlinecap } def
/j { setlinejoin } def
/cm { [ 7 1 roll ] concat } def
/q { gsave } def
/Q { grestore } def
/g { setgray } def
/rg { setrgbcolor } def
/G { setgray } def
/RG { setrgbcolor } def
/S { stroke } def
/f* { eofill } def
/f { fill } def
/ipeMakeFont {
  exch findfont
  dup length dict begin
    { 1 index /FID ne { def } { pop pop } ifelse } forall
    /Encoding exch def
    currentdict
  end
  definefont pop
} def
/ipeFontSize 0 def
/Tf { dup /ipeFontSize exch store selectfont } def
/Td { translate } def
/BT { gsave } def
/ET { grestore } def
/TJ { 0 0 moveto { dup type /stringtype eq
 { show } { ipeFontSize mul -0.001 mul 0 rmoveto } ifelse
} forall } def
end
%%EndResource
%%EndProlog
%%BeginSetup
ipe begin
%%BeginResource: font GKTLJX+CMR10
%!PS-AdobeFont-1.1: CMR10 1.00B
%%CreationDate: 1992 Feb 19 19:54:52
% Copyright (C) 1997 American Mathematical Society. All Rights Reserved.
11 dict begin
/FontInfo 7 dict dup begin
/version (1.00B) readonly def
/Notice (Copyright (C) 1997 American Mathematical Society. All Rights Reserved) readonly def
/FullName (CMR10) readonly def
/FamilyName (Computer Modern) readonly def
/Weight (Medium) readonly def
/ItalicAngle 0 def
/isFixedPitch false def
end readonly def
/FontName /GKTLJX+CMR10 def
/PaintType 0 def
/FontType 1 def
/FontMatrix [0.001 0 0 0.001 0 0] readonly def
/Encoding 256 array
0 1 255 {1 index exch /.notdef put} for
dup 40 /parenleft put
dup 41 /parenright put
dup 48 /zero put
dup 49 /one put
dup 50 /two put
dup 51 /three put
dup 97 /a put
dup 98 /b put
dup 99 /c put
dup 100 /d put
dup 101 /e put
dup 102 /f put
dup 103 /g put
dup 104 /h put
dup 105 /i put
dup 110 /n put
dup 111 /o put
dup 114 /r put
dup 116 /t put
dup 118 /v put
dup 120 /x put
readonly def
/FontBBox{-251 -250 1009 969}readonly def
/UniqueID 5000793 def
currentdict end
currentfile eexec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0000000000000000000000000000000000000000000000000000000000000000
0000000000000000000000000000000000000000000000000000000000000000
0000000000000000000000000000000000000000000000000000000000000000
0000000000000000000000000000000000000000000000000000000000000000
0000000000000000000000000000000000000000000000000000000000000000
0000000000000000000000000000000000000000000000000000000000000000
0000000000000000000000000000000000000000000000000000000000000000
0000000000000000000000000000000000000000000000000000000000000000
cleartomark
%%EndResource
/F8 /GKTLJX+CMR10
[ /Gamma/Delta/Theta/Lambda/Xi/Pi/Sigma/Upsilon
  /Phi/Psi/Omega/ff/fi/fl/ffi/ffl
  /dotlessi/dotlessj/grave/acute/caron/breve/macron/ring
  /cedilla/germandbls/ae/oe/oslash/AE/OE/Oslash
  /suppress/exclam/quotedblright/numbersign/dollar/percent/ampersand/quoteright
  /parenleft/parenright/asterisk/plus/comma/hyphen/period/slash
  /zero/one/two/three/four/five/six/seven
  /eight/nine/colon/semicolon/exclamdown/equal/questiondown/question
  /at/A/B/C/D/E/F/G
  /H/I/J/K/L/M/N/O
  /P/Q/R/S/T/U/V/W
  /X/Y/Z/bracketleft/quotedblleft/bracketright/circumflex/dotaccent
  /quoteleft/a/b/c/d/e/f/g
  /h/i/j/k/l/m/n/o
  /p/q/r/s/t/u/v/w
  /x/y/z/endash/emdash/hungarumlaut/tilde/dieresis
  /suppress/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /space/Gamma/Delta/Theta/Lambda/Xi/Pi/Sigma
  /Upsilon/Phi/Psi/.notdef/.notdef/Omega/ff/fi
  /fl/ffi/ffl/dotlessi/dotlessj/grave/acute/caron
  /breve/macron/ring/cedilla/germandbls/ae/oe/oslash
  /AE/OE/Oslash/suppress/dieresis/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef ]
ipeMakeFont
%%BeginResource: font JPAIIC+CMMI10
%!PS-AdobeFont-1.1: CMMI10 1.100
%%CreationDate: 1996 Jul 23 07:53:57
% Copyright (C) 1997 American Mathematical Society. All Rights Reserved.
11 dict begin
/FontInfo 7 dict dup begin
/version (1.100) readonly def
/Notice (Copyright (C) 1997 American Mathematical Society. All Rights Reserved) readonly def
/FullName (CMMI10) readonly def
/FamilyName (Computer Modern) readonly def
/Weight (Medium) readonly def
/ItalicAngle -14.04 def
/isFixedPitch false def
end readonly def
/FontName /JPAIIC+CMMI10 def
/PaintType 0 def
/FontType 1 def
/FontMatrix [0.001 0 0 0.001 0 0] readonly def
/Encoding 256 array
0 1 255 {1 index exch /.notdef put} for
dup 59 /comma put
readonly def
/FontBBox{-32 -250 1048 750}readonly def
/UniqueID 5087385 def
currentdict end
currentfile eexec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%%EndResource
/F11 /JPAIIC+CMMI10
[ /Gamma/Delta/Theta/Lambda/Xi/Pi/Sigma/Upsilon
  /Phi/Psi/Omega/alpha/beta/gamma/delta/epsilon1
  /zeta/eta/theta/iota/kappa/lambda/mu/nu
  /xi/pi/rho/sigma/tau/upsilon/phi/chi
  /psi/omega/epsilon/theta1/pi1/rho1/sigma1/phi1
  /arrowlefttophalf/arrowleftbothalf/arrowrighttophalf/arrowrightbothalf/arrowhookleft/arrowhookright/triangleright/triangleleft
  /zerooldstyle/oneoldstyle/twooldstyle/threeoldstyle/fouroldstyle/fiveoldstyle/sixoldstyle/sevenoldstyle
  /eightoldstyle/nineoldstyle/period/comma/less/slash/greater/star
  /partialdiff/A/B/C/D/E/F/G
  /H/I/J/K/L/M/N/O
  /P/Q/R/S/T/U/V/W
  /X/Y/Z/flat/natural/sharp/slurbelow/slurabove
  /lscript/a/b/c/d/e/f/g
  /h/i/j/k/l/m/n/o
  /p/q/r/s/t/u/v/w
  /x/y/z/dotlessi/dotlessj/weierstrass/vector/tie
  /psi/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /space/Gamma/Delta/Theta/Lambda/Xi/Pi/Sigma
  /Upsilon/Phi/Psi/.notdef/.notdef/Omega/alpha/beta
  /gamma/delta/epsilon1/zeta/eta/theta/iota/kappa
  /lambda/mu/nu/xi/pi/rho/sigma/tau
  /upsilon/phi/chi/psi/tie/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef
  /.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef/.notdef ]
ipeMakeFont
%%EndSetup
q 1 0 0 1 -156.764 -42.93 cm 1 0 0 1 0 0 cm 0 0 0 rg 0 0 0 RG
1 0 0 1 0 -785.58 cm
BT
/F8 9.963 Tf 0 785.58 Td[(v)28(extex)-334(0)]TJ
ET
Q
q 1 0 0 1 -30.2803 -44.2787 cm 1 0 0 1 0 0 cm 0 0 0 rg 0 0 0 RG
1 0 0 1 0 -785.58 cm
BT
/F8 9.963 Tf 0 785.58 Td[(v)28(ertex)-333(1)]TJ
ET
Q
q 1 0 0 1 9.747 42.93 cm 1 0 0 1 0 0 cm 0 0 0 rg 0 0 0 RG
1 0 0 1 0 -785.58 cm
BT
/F8 9.963 Tf 0 785.58 Td[(v)28(ertex)-333(2)]TJ
ET
Q
q 1 0 0 1 -120.213 81.81 cm 1 0 0 1 0 0 cm 0 0 0 rg 0 0 0 RG
1 0 0 1 0 -785.58 cm
BT
/F8 9.963 Tf 0 785.58 Td[(v)28(ertex)-333(3)]TJ
ET
Q
q np
-16.245 27.54 m
-64.1677 51.03 l
-38.1758 -8.1 l
h 0.6 g q f* Q 0.4 w 0 g S
Q
q np
-64.1677 51.03 m
-92.5965 -9.72 l
-38.1758 -8.1 l
0.4 w S
Q
q np
-92.5965 -9.72 m
-16.245 27.54 l
q f* Q [1 3] 0 d 0.4 w S
Q
q 1 0 0 1 9.747 -13.77 cm 1 0 0 1 0 0 cm 0 0 0 rg 0 0 0 RG
1 0 0 1 0 -785.081 cm
BT
/F8 9.963 Tf 0 785.081 Td[(facet)-334(0)]TJ
ET
Q
q np
-64.1677 51.03 m
-30.0532 80.19 l
-38.1758 -8.1 l
-16.245 27.54 l
-30.0532 80.19 l
[4] 0 d 0.4 w S
Q
q 1 0 0 1 15.4327 81 cm 1 0 0 1 0 0 cm 1 0 0 1 0 1.937 cm
0 0 0 rg 0 0 0 RG
1 0 0 1 0 -785.081 cm
BT
/F8 9.963 Tf 0 785.081 Td[(neigh)29(b)-28(or)-333(0)]TJ
ET
Q
q 1 0 0 1 -151.891 42.93 cm 1 0 0 1 0 0 cm 1 0 0 1 0 2.491 cm
0 0 0 rg 0 0 0 RG
1 0 0 1 0 -784.528 cm
BT
/F8 9.963 Tf 0 784.528 Td[(edge)-334(\0501)]TJ/F11 9.963 Tf 31.548 0 Td[(;)]TJ/F8 9.963 Tf 4.428 0 Td[(3\051)]TJ
ET
Q
q np
7.31028 -0.81007 m
-2.11284 17.3882 -21.9142 27.747 -42.237 25.1099 c
[1] 0 d 0.4 w S
q np
-42.237 25.1099 m
-34.9949 23.6967 l
-35.5954 28.3246 l
h q f* Q [] 0 d S
Q
Q
q np
-104.78 37.26 m
-96.2646 22.8333 -77.6662 18.0415 -63.2396 26.5571 c
-61.2265 27.7454 -59.3575 29.1624 -57.6698 30.78 c
[1] 0 d 0.4 w S
q np
-57.6698 30.78 m
-64.338 27.6209 l
-61.1089 24.2518 l
h q f* Q [] 0 d S
Q
Q
q np
-78.7882 72.09 m
-64.1677 51.03 l
[1] 0 d 0.4 w S
q np
-64.1677 51.03 m
-66.2429 58.1108 l
-70.0764 55.4495 l
h q f* Q [] 0 d S
Q
Q
q np
-34.1145 -31.59 m
-38.1758 -8.1 l
[1] 0 d 0.4 w S
q np
-38.1758 -8.1 m
-39.2825 -15.3952 l
-34.684 -14.6001 l
h q f* Q [] 0 d S
Q
Q
q np
-111.278 -28.35 m
-92.5965 -9.72 l
[1] 0 d 0.4 w S
q np
-92.5965 -9.72 m
-99.2007 -13.0107 l
-95.9054 -16.3151 l
h q f* Q [] 0 d S
Q
Q
q np
4.06125 42.93 m
-16.245 27.54 l
[1] 0 d 0.4 w S
q np
-16.245 27.54 m
-9.25683 29.9085 l
-12.0756 33.6277 l
h q f* Q [] 0 d S
Q
Q
q np
-29.6619 60.0906 m
-12.5604 59.8676 3.83545 66.8983 15.4631 79.4406 c
[1] 0 d 0.4 w S
q np
-29.6619 60.0906 m
-22.6929 57.6662 l
-22.6321 62.3325 l
h q f* Q [] 0 d S
Q
Q
showpage
%%BeginIpeXml: /FlateDecode
%GhTQ+gQ'uA&:N^l7b0c1;DZrH?Rsd,^@WAE4p3PND'2]3fPSC;'3M]I>dpZk+V&"S3HO?B%#R*^
%3]3t4=q67M;NlWNPOc.sL>k96*D39"ZnBc_FN-b<'_tR9j8pgo+>Zoj-3IF5%/_prd5r]sH\5cf
%XgVnW1)/W[C+681P0/uqQEdG]0eJE%8pUN5;=u[Yj//i%+o7-ukHi*f1B3UX=QYMu'R<j-o8Rb-
%Dduu<re>gU(@-TYD0fp*]A>HJg892U9qsHc57+8=5+a:i3!R::BoF$RqlAj2ANi__JELCicA-uk
%=uOW(0q5YR*?$M;P3U=>"g%=$R'EN`Wbnd?q^HtMKTb>7bp:^Fa?dS9ZX\-@#>*E2'([fUIILm:
%=B+/T)jR:qU97GRS/Mp)jLCP0^-N:+=V+"5'F+6&GgICj3cF3MGta.%A/o\b.4r/siC_F\6Z"Af
%O4M+@QT;t.iB::sSA):q&U0/B!A],pqn+lOGOT7XlpDj]0m;&[UU-&%:@nkZMo]-%Y:l)e`M39,
%5X.OUH9HAUGSf>1P:IB3.7/N]DKS?]_g\V[$Ff,3RZCCZf0ZB%I>uMl`HJ%*\!aV!+\5T&KVIpf
%5<<ah%;!qKp2I0NNQ(,ZJJQtQBq[G%G@$;@GA:)72[pM@EJLJF2)C;_5l*,QO2fWf)iZ1N@Zr.F
%":q-S(]+&K98:r)L*1E'lC"2`Xuu8lTK*UI)dZ._feNRk;#n_Qh)_<+5/)t4nU(Uk1Uo;fco_bJ
%N0%E7MX*Yc,n9\NjAA!N_%f52j+bNjSb[f]"q+J,o7:UYO3Y_kmGP>FP@+WSX2/S9n^O)>f)[Ut
%i_%NfEW9s'ird3\()`D&DDKL:5dI<-fps/m8uK&P<qiH,o`=h")u#!C_;uXq^>gpZAQKAS10s%S
%EnIM+&;)>8h5t7JJSkZO;W&c<_oMK9U&Xt/e*$2)a5.p5A=j2~>
%%EndIpeXml
%%Trailer
end
%%EOF
