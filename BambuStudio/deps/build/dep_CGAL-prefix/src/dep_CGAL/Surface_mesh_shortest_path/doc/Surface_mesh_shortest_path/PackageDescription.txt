/// \defgroup PkgSurfaceMeshShortestPathRef Triangulated Surface Mesh Geodesic Shortest Paths Reference

/// \defgroup PkgSurfaceMeshShortestPathConcepts Concepts
/// \ingroup PkgSurfaceMeshShortestPathRef

/// \defgroup PkgSurfaceMeshShortestPathTraitsClasses Traits Classes
/// \ingroup PkgSurfaceMeshShortestPathRef

/*!
\addtogroup PkgSurfaceMeshShortestPathRef
\todo Modify the algorithm to support more efficient incremental construction
\todo Add parallelization to the algorithm
\todo Add methods for computing the ridge tree using the output of the algorithm
\todo Add methods for computing shortest paths from geodesic sources as well

\cgalPkgDescriptionBegin{Triangulated Surface Mesh Shortest Paths,PkgSurfaceMeshShortestPath}
\cgalPkgPicture{shortestpathspackage-ico.png}
\cgalPkgSummaryBegin
\cgalPkgAuthors{<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>}
\cgalPkgDesc{The package provides methods for computing geodesic shortest path on triangulated surface meshes.  The algorithm used is based on a paper by <PERSON><PERSON> and <PERSON> \cgalCite{XinWang2009improving<PERSON><PERSON>han} .  The input of this package can be any model of the `FaceListGraph` concept. }
\cgalPkgManuals{Chapter_Surface_mesh_shortest_path,PkgSurfaceMeshShortestPathRef}
\cgalPkgSummaryEnd
\cgalPkgShortInfoBegin
\cgalPkgSince{4.7}
\cgalPkgBib{cgal:klcdv-tsmsp}
\cgalPkgLicense{\ref licensesGPL "GPL"}
\cgalPkgDemo{Polyhedron demo,polyhedron_3.zip}
\cgalPkgShortInfoEnd
\cgalPkgDescriptionEnd

\cgalClassifedRefPages

\cgalCRPSection{Concepts}

- `SurfaceMeshShortestPathTraits`
- `SurfaceMeshShortestPathVisitor`

\cgalCRPSection{Classes}
- `CGAL::Surface_mesh_shortest_path<SurfaceMeshShortestPathTraits,VertexIndexMap, HalfedgeIndexMap, FaceIndexMap, VertexPointMap>`
- `CGAL::Surface_mesh_shortest_path_traits<Kernel, FaceListGraph>`

\cgalCRPSection{Enums}

- `CGAL::Surface_mesh_shortest_paths_3::Barycentric_coordinates_type`

*/


/*!
 \namespace CGAL::Surface_mesh_shortest_paths_3

 \brief Namespace containing support members specific to this package.
 */

