// Copyright (c) 2014 GeometryFactory
// All rights reserved.
//
// This file is part of CGAL (www.cgal.org).
//
// $URL$
// $Id$
// SPDX-License-Identifier: GPL-3.0-or-later OR LicenseRef-Commercial
//
// Author(s)     : <PERSON>

#ifndef CGAL_SURFACE_MESH_SHORTEST_PATH_H
#define CGAL_SURFACE_MESH_SHORTEST_PATH_H

#include <CGAL/license/Surface_mesh_shortest_path.h>


/**
 * \ingroup PkgSurfaceMeshShortestPathRef
 * \file CGAL/Surface_mesh_shortest_path.h
 * Convenience header file only including `CGAL/Surface_mesh_shortest_path/Surface_mesh_shortest_path.h`
 * and `CGAL/Surface_mesh_shortest_path/Surface_mesh_shortest_path_traits.h`.
 */

#include <CGAL/Surface_mesh_shortest_path/Surface_mesh_shortest_path.h>
#include <CGAL/Surface_mesh_shortest_path/Surface_mesh_shortest_path_traits.h>

#endif // CGAL_SURFACE_MESH_SHORTEST_PATH_H
