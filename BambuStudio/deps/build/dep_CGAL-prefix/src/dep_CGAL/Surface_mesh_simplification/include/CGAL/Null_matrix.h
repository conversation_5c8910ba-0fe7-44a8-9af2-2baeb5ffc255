// Copyright (c) 2006  GeometryFactory (France). All rights reserved.
//
// This file is part of CGAL (www.cgal.org).
//
// $URL$
// $Id$
// SPDX-License-Identifier: GPL-3.0-or-later OR LicenseRef-Commercial
//
// Author(s)     : <PERSON> <<EMAIL>>
//
#ifndef CGAL_NULL_MATRIX_H
#define CGAL_NULL_MATRIX_H

#include <CGAL/license/Surface_mesh_simplification.h>

namespace CGAL {

class Null_matrix {};

static const Null_matrix NULL_MATRIX = Null_matrix();

} // namespace CGAL

#endif // CGAL_NULL_MATRIX_H
