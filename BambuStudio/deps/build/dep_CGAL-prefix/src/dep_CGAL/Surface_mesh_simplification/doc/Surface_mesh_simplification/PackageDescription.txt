/// \defgroup PkgSurfaceMeshSimplificationRef Triangulated Surface Mesh Simplification Reference
/// \defgroup PkgSurfaceMeshSimplificationConcepts Concepts
/// \ingroup PkgSurfaceMeshSimplificationRef
/*!
\addtogroup PkgSurfaceMeshSimplificationRef
\cgalPkgDescriptionBegin{Triangulated Surface Mesh Simplification,PkgSurfaceMeshSimplification}
\cgalPkgPicture{SMS-detail.png}
\cgalPkgSummaryBegin
\cgalPkgAuthor{<PERSON>, <PERSON><PERSON>, and Baskın Şenbaşlar}
\cgalPkgDesc{This package provides an algorithm to simplify a triangulated surface mesh by edge collapsing.
             Users can define cost, constraints, and placement strategies to decide when and how should
             edges be collapsed. A few strategies are offered by default, such as the Turk/Lindstrom
             and Garland-Heckbert <I>memoryless</I> approaches.}
\cgalPkgManuals{Chapter_Triangulated_Surface_Mesh_Simplification,PkgSurfaceMeshSimplificationRef}
\cgalPkgSummaryEnd
\cgalPkgShortInfoBegin
\cgalPkgSince{3.3}
\cgalPkgBib{cgal:c-tsms-12}
\cgalPkgLicense{\ref licensesGPL "GPL"}
\cgalPkgDemo{Polyhedron demo,polyhedron_3.zip}
\cgalPkgShortInfoEnd
\cgalPkgDescriptionEnd

\cgalClassifedRefPages

\cgalCRPSection{Concepts}
- `StopPredicate`
- `GetCost`
- `GetPlacement`
- `PlacementFilter`
- `EdgeCollapseSimplificationVisitor`

\cgalCRPSection{Functions}
- `CGAL::Surface_mesh_simplification::edge_collapse()`

\cgalCRPSection{Classes}
- `CGAL::Surface_mesh_simplification::Edge_collapse_visitor_base<TriangleMesh>`
- `CGAL::Surface_mesh_simplification::Edge_profile<TriangleMesh, VertexPointMap, GeomTraits>`
- `CGAL::Surface_mesh_simplification::Count_stop_predicate<TriangleMesh>`
- `CGAL::Surface_mesh_simplification::Count_ratio_stop_predicate<TriangleMesh>`
- `CGAL::Surface_mesh_simplification::Edge_length_stop_predicate<FT>`
- `CGAL::Surface_mesh_simplification::Edge_length_cost<TriangleMesh>`
- `CGAL::Surface_mesh_simplification::Midpoint_placement<TriangleMesh>`
- `CGAL::Surface_mesh_simplification::LindstromTurk_cost<TriangleMesh>`
- `CGAL::Surface_mesh_simplification::LindstromTurk_placement<TriangleMesh>`
- `CGAL::Surface_mesh_simplification::GarlandHeckbert_policies<TriangleMesh, GeomTraits>`
- `CGAL::Surface_mesh_simplification::Bounded_normal_change_placement<Placement>`
- `CGAL::Surface_mesh_simplification::Bounded_normal_change_filter<Filter>`
- `CGAL::Surface_mesh_simplification::Polyhedral_envelope_filter<Filter>`
- `CGAL::Surface_mesh_simplification::Constrained_placement<Placement, TriangleMesh>`
*/
