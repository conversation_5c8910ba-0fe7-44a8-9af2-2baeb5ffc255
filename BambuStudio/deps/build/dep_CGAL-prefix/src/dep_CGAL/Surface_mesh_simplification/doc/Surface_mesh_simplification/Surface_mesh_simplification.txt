namespace CGAL {
/*!

\mainpage User Manual
\anchor Chapter_Triangulated_Surface_Mesh_Simplification

\cgalAutoToc

\authors <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>

\image html Illustration-Simplification-ALL.jpg
\image latex Illustration-Simplification-ALL.jpg

\section Surface_mesh_simplificationIntroduction Introduction

Surface mesh simplification is the process of reducing the number of faces used in a surface mesh while
keeping the overall shape, volume and boundaries preserved as much as possible.
It is the opposite of subdivision.

The algorithm presented here can simplify any <I>oriented 2-manifold surface</I>,
with any number of connected components, with or without boundaries (border or holes)
and handles (arbitrary genus), using a method known as <I>edge collapse</I>.
Roughly speaking, the method consists of iteratively replacing an edge with a single vertex,
removing 2 triangles per collapse.

Edges are collapsed according to a priority given by a user-supplied <I>cost</I> function,
and the coordinates of the replacing vertex are determined by another user-supplied
<I>placement</I> function. The algorithm terminates when a user-supplied <I>stop predicate</I>
is met, such as reaching the desired number of edges.

The algorithm implemented here is generic in the sense that it does not require the surface mesh
to be of a particular type but to be a model of the `MutableFaceGraph` and `HalfedgeListGraph` concepts.
We give examples for `Surface_mesh`, `Polyhedron_3`, and OpenMesh.

The design is <A HREF="https://en.wikipedia.org/wiki/Policy-based_design"><I>policy-based</I></A>
(<A HREF="https://en.wikipedia.org/wiki/Policy-based_design"><TT>https://en.wikipedia.org/wiki/Policy-based_design</TT></A>),
meaning that you can customize some aspects of the process by passing a set of
<I>policy objects</I>. Each policy object specifies a particular aspect of the algorithm,
such as how edges are selected and where the replacement vertex is placed. All policies have
a sensible default.
Furthermore, the API uses the so-called `named-parameters` technique which allows you
to pass only the relevant parameters, in any order, omitting those parameters whose
default is appropriate.

\section Surface_mesh_simplificationOverview Overview of the Simplification Process

The free function that implements the simplification algorithm takes not only the surface mesh
and the desired stop predicate but a number of additional parameters which control and
monitor the simplification process. This section briefly describes the process in order
to set the background for the discussion of the parameters to the algorithm.

There are two slightly different "edge" collapse operations. One is known as
<I>edge-collapse</I> while the other is known as <I>halfedge-collapse</I>.
Given an edge `e` joining vertices `w` and `v`, the edge-collapse operation replaces
`e`,`w` and `v` for a new vertex `r`, while the halfedge-collapse operation
pulls `v` into `w`, eliminating `e` and leaving `w` in place.
In both cases the operation removes the edge `e` along with the 2 triangles
adjacent to it.

This package uses the halfedge-collapse operation, which is implemented by removing,
additionally, 1 vertex (`v`) and 2 edges, one per adjacent triangle.
It optionally moves the remaining vertex (`w`) into a new position,
called <I>placement</I>, in which case the net effect is the same as in
the edge-collapse operation.

Naturally, the surface mesh that results from an edge collapse deviates from the initial
surface mesh by some amount, and since the goal of simplification is to reduce the number
of triangles while retaining the overall look of the surface mesh as much as possible,
it is necessary to measure such a deviation. Some methods attempt to measure the
total deviation from the initial surface mesh to the completely simplified surface mesh,
for example, by tracking an accumulated error while keeping a history of the simplification
changes. Other methods, like the ones implemented in this package, attempt to measure only
the <I>cost</I> of each individual edge collapse (the local deviation introduced by
a single simplification step) and plan the entire process as a sequence of steps
of increasing cost.

Global error tracking methods produce highly accurate simplifications but take up a lot
of additional space. Cost-driven methods, like the ones in this package, produce slightly
less accurate simplifications but take up much less additional space, even none in some cases.

This package provides two cost-driven methods. The first cost-driven method implemented in this package,
namely Lindstrom&Turk, is mainly based on \cgalCite{cgal:lt-fmeps-98}, \cgalCite{cgal:lt-ems-99},
with contributions from \cgalCite{hddms-mo-93}, \cgalCite{gh-ssqem-97} and \cgalCite{degn-tpec-98}.
The second cost-driven method implemented in this package, namely Garland&Heckbert, is mainly based on
\cgalCite{gh-ssqem-97}.

The algorithm proceeds in two stages. In the first stage, called <I>collection stage</I>,
an initial <I>collapse cost</I> is assigned to each and every edge in the surface mesh.
Then in the second stage, called <I>collapsing stage</I>, edges are
processed in order of increasing cost. Some processed edges are collapsed
while some are just discarded. Collapsed edges are replaced by a vertex and the collapse
cost of all the edges now incident on the replacement vertex is recalculated, affecting
the order of the remaining unprocessed edges.

Not all edges selected for processing are collapsed. A processed edge can be discarded
right away, without being collapsed, if it does not satisfy certain topological
and geometric conditions.

The algorithm presented in \cgalCite{gh-ssqem-97} contracts (collapses) arbitrary
vertex pairs and not only edges by considering certain vertex pairs as forming
a pseudo-edge and proceeding to collapse both edges and pseudo-edges in the
same way as in \cgalCite{cgal:lt-fmeps-98},
\cgalCite{cgal:lt-ems-99}. However, contracting an arbitrary
vertex-pair may result in a
non-manifold surface mesh, but the current state of this package can only deal
with manifold surface meshes, thus, it can only collapse edges.
That is, this package cannot be used as a framework for vertex contraction.
Therefore, our implementation of \cgalCite{gh-ssqem-97} only collapses edges.

\section Surface_mesh_simplificationCost Cost Strategy

The specific way in which the collapse cost and vertex placement is
calculated is called the <I>cost strategy</I>. The user can choose
different strategies in the form of policies and related parameters,
passed to the algorithm.

The current version of the package provides a set of policies implementing
three strategies: the Lindstrom-Turk strategy, which is the default,
the Garland-Heckbert strategy, and
a strategy consisting of an edge-length cost with an optional
midpoint placement (much faster but less accurate).

\subsection SurfaceMeshSimplificationLindstromTurkStrategy Lindstrom-Turk Cost and Placement Strategy

The main characteristic of the strategy presented in
\cgalCite{cgal:lt-fmeps-98}, \cgalCite{cgal:lt-ems-99} is that the simplified surface mesh
is not compared at each step with the original surface mesh (or the surface mesh
at a previous step) so there is no need to keep extra information,
such as the original surface mesh or a history of the local changes. Hence
the name <I>memoryless</I> simplification.

At each step, all remaining edges are potential candidates for
collapsing and the one with the lowest cost is selected.
The cost of collapsing an edge is given by the position chosen for the
vertex that replaces it.

The replacement vertex position is computed as
the solution to a system of 3 linearly-independent linear equality constraints.
Each constraint is obtained by minimizing a quadratic objective function
subject to the previously computed constraints.
There are several possible candidate constraints and each is considered in order of importance.
A candidate constraint might be <I>incompatible</I> with the previously accepted constraints,
in which case it is rejected and the next constraint is considered.
Once 3 constraints have been accepted, the system is solved for the vertex position.
The first constraints considered preserves the shape of the surface mesh boundaries
(in case the edge profile has boundary edges).
The next constraints preserve the total volume of the surface mesh.
The next constraints, if needed, optimize the local changes in volume and boundary shape.
Lastly, if a constraint is still needed (because the ones previously computed were incompatible),
a third (and last) constraint is added to favor equilateral triangles over elongated triangles.

The cost is then a weighted sum of the shape, volume and boundary optimization terms, where the user specifies the unit <I>weighting unit factor</I> for each term.

The local changes are computed independently for each edge using only
the triangles currently adjacent to it at the time when the edge
is about to be collapsed, that is, after all previous collapses.
Thus, the transitive path of minimal local changes yields at
the end a global change reasonably close to the absolute minimum.

\subsection SurfaceMeshSimplificationGarlandHeckbertStrategy Garland-Heckbert Cost and Placement Strategy

As in the case of the Lindstrom-Turk strategy, the Garland-Heckbert strategy presented in \cgalCite{gh-ssqem-97}
does not compare the resulting mesh with the original mesh and does not depend on
history of local changes. Instead, it encodes approximate distance to the original
mesh by using quadric matrices that it assigns to each vertex.

In the beginning, a quadric matrix \f$ Q \f$ is assigned to each vertex \f$ v \f$ and encodes the total
squared distance that any point \f$ p \f$ has to \f$ v \f$'s neighboring faces, which is given by the matrix
product \f$ p'Qp \f$.
At each step, the edge which minimizes the cost of collapsing is selected for
the collapse operation. The cost of collapsing an edge is calculated by
minimizing the error function \f$ p'Qp \f$ where \f$ Q \f$ is the combined quadric
matrices of the edge's endpoints
and \f$ p \f$ is the point that minimizes the cost (i.e., decision variables). The point
\f$ p \f$ that minimizes the objective function is picked as the new placement point.
Since the error function is quadratic, finding its minimum can be done by simply calculating
the gradient and equating it to zero. If this fails due to a singularity, the optimal point
and the cost are found on the edge.
After placing the new vertex, a new quadric matrix is assigned to it by simply summing the
quadric matrices of the two extremities of the edge that has been collapsed.

Additional pseudo-faces that are perpendicular to neighboring faces
are added to quadric matrices of border vertices in order to preserve sharp borders of
the mesh as much as possible.

\subsection Surface_mesh_simplificationCostStrategyPolicies Cost Strategy Policies

The cost strategy used by the algorithm is selected by means of three policies:
`GetPlacement`, `GetCost`, and `Filter`.

The `GetPlacement` policy is called to compute the new position
for the remaining vertex after the halfedge-collapse. It returns
an optional value, which can be absent if the edge should not be collapsed.

The `GetCost` policy is called to compute the cost
of collapsing an edge. This policy uses the placement to compute
the cost (which is an error measure) and determines the
ordering of the edges.

The algorithm maintains an internal data structure (a mutable priority queue)
which allows each edge to be processed in increasing cost order. Such a data structure
requires some per-edge additional information, such as the edge's cost.
If the record of per-edge additional information occupies N bytes of storage,
simplifying a surface mesh of 1 million edges (a normal size) requires 1 million times N bytes
of additional storage. Thus, to minimize the amount of additional memory required to
simplify a surface mesh only the cost is attached to each edge and nothing else.

But this is a trade-off: the cost of a collapse is a function of the placement
(the new position chosen for the remaining vertex) so before `GetCost`
is called for each and every edge, `GetPlacement` must also be called to obtain
the placement parameter to the cost function.
But that placement, which is a 3D point, is not attached to each and every edge since
that would easily <I>triple</I> the additional storage requirement.
On the one hand, this dramatically saves on memory but on the other hand is
a processing waste because when an edge is effectively collapsed, `GetPlacement`
must be called <I>again</I> to know were to move the remaining vertex.

Earlier prototypes showed that attaching the placement to the edge, thus avoiding one
redundant call to the placement function after the edge collapsed, has little
impact on the total running time. This is because the cost of an each edge is not just
computed once but changes several times during the process so the placement function
must be called several times just as well. Caching the placement can only avoid the
very last call, when the edge is collapsed, but not all the previous calls which
are needed because the placement (and cost) changes.


Finally, we explain the `PlacementFilter` policy.
While the cost is a scalar that is used in the priority queue, there may be
additional criteria coming in to decide if the edge collapse shall be performed or not.
While such a criterion could be easily integrated into the cost function,
that is setting the cost to infinity in order not to be considered a candidate for a collapse,
we test a criterion only for an edge when it is the next edge to collapse.
This makes the mesh  simplification faster in case the computation of the
criterion is expensive, for example when we check if the simplified mesh
is in a tolerance envelope of the input mesh.


\section Surface_mesh_simplificationAPI API

\subsection Surface_mesh_simplificationAPIOverview API Overview

Since the algorithm is free from robustness issues there is no need for exact predicates nor constructions and `Simple_cartesian<double>` can be used safely.
\cgalFootnote{In the current version, 3.3, the LindstromTurk policies are not implemented for homogeneous coordinates, so a %Cartesian kernel must be used.}

The simplification algorithm is implemented as the free template function
`Surface_mesh_simplification::edge_collapse()`. The function has two mandatory and several optional parameters.

\subsection Surface_mesh_simplificationMandatoryParameters Mandatory Parameters

There are two main parameters to the algorithm: the surface mesh to be simplified (in-place) and the stop predicate.

The surface mesh to simplify must be a model of the `MutableFaceGraph` and `HalfedgeListGraph` concepts.

The stop predicate is called after each edge is selected for processing, <I>before</I>
it is classified as collapsible or not (thus before it is collapsed). If the stop predicate
returns `true` the algorithm terminates.

\subsection Surface_mesh_simplificationOptionalNamed Optional Named Parameters

The notion of <I>named parameters</I> was also introduced in the \bgl. You can read about it in \cgalCite{cgal:sll-bgl-02} or the following site: <A HREF="https://www.boost.org/libs/graph/doc/bgl_named_params.html"><TT>https://www.boost.org/libs/graph/doc/bgl_named_params.html</TT></A>. Named parameters allow the user to specify only those parameters which are really needed, by name, making the parameter ordering unimportant.

Say there is a function `f()` that takes 3 parameters called `name`, `age` and `gender`, and you have variables `n,a and g` to pass as parameters to that function. Without named parameters, you would call it like this: `f(n,a,g)`, but with named parameters, you call it like this: `f(name(n).age(a).gender(g))`.

That is, you give each parameter a name by wrapping it into a function whose name matches that of the parameter. The entire list of named parameters is really a composition of function calls separated by a dot (\f$ .\f$). Thus, if the function takes a mix of mandatory and named parameters, you use a comma to separate the last non-named parameter from the first named parameters, like this:

`f(non_named_par0, non_named_pa1, name(n).age(a).gender(g)) `

When you use named parameters, the ordering is irrelevant, so this: `f(name(n).age(a).gender(g))` is equivalent to this:
`f(age(a).gender(g).name(n))`, and you can just omit any named parameter that has a default value.

\subsection Surface_mesh_simplificationSampleCall Sample Call

\code{.cpp}

/*
surface_mesh                  : the surface_mesh to simplify
stop_predicate                : policy indicating when the simplification must finish
vertex_index_map(vimap)       : property-map giving each vertex a unique integer index
edge_index_map(eimap)         : property-map giving each edge a unique integer index
edge_is_constrained_map(ebmap): property-map specifying whether an edge is a constrained edge or not
get_cost(cf)                  : function object computing the cost of a collapse
get_placement(pf)             : function object computing the placement for the remaining vertex
filter(filter)                : function object to reject a candidate chosen for the next edge collapse
visitor(vis)                  : function object tracking the simplification process
*/

int r = edge_collapse(surface_mesh, stop_predicate,
                      CGAL::parameters::vertex_index_map(vimap)
                                       .edge_index_map(eimap)
                                       .edge_is_border_map(ebmap)
                                       .get_cost(cf)
                                       .get_placement(pf)
                                       .filter(filter
                                       .visitor(vis));

\endcode

\section Surface_mesh_simplificationExamples Examples

\subsection Surface_mesh_simplificationExampleUsingSurfaceMesh Example Using a Surface_mesh

The following example illustrates the simplification of a `Surface_mesh`.
The unspecified cost strategy defaults to Lindstrom-Turk.

\cgalExample{Surface_mesh_simplification/edge_collapse_surface_mesh.cpp}

\subsection Surface_mesh_simplificationExampleUsinga Example Using a Default Polyhedron

The following example illustrates the simplification of a `Polyhedron_3` with default
vertices, halfedges, and faces.
The unspecified cost strategy defaults to Lindstrom-Turk.

\cgalExample{Surface_mesh_simplification/edge_collapse_polyhedron.cpp}

\subsection Surface_mesh_simplificationExampleUsingan Example Using an Enriched Polyhedron

The following example is equivalent to the previous example but using an
enriched polyhedron whose halfedges support an `id` field to
store the edge index needed by the algorithm.

\cgalExample{Surface_mesh_simplification/edge_collapse_enriched_polyhedron.cpp}

\subsection Surface_mesh_simplificationExampleOpenMesh Example for Simplification of OpenMesh

The following example shows how the mesh simplification package can be applied
on a mesh data structure which is not part of \cgal, but a model of `FaceGraph`.

What is particular in this example is the property map that allows to associate
3D \cgal points to the vertices.

\cgalExample{Surface_mesh_simplification/edge_collapse_OpenMesh.cpp}

\subsection Surface_mesh_simplificationExamplewithedges Example with Edges Marked as Non-Removable

The following example shows how to use the optional named parameter `edge_is_constrained_map` to prevent
edges from being removed. Edges marked as constrained are guaranteed to be in the final surface mesh. However,
the vertices of the constrained edges may change and the placement may change the points.
The wrapper `CGAL::Surface_mesh_simplification::Constrained_placement` guarantees that these points are not changed.

\cgalExample{Surface_mesh_simplification/edge_collapse_constrained_border_surface_mesh.cpp}


\subsection Surface_mesh_simplificationExamplewithBoundedNormal Example with Bounded Change of Face Normals

The surface mesh simplification does not guarantee that the resulting surface
has no self intersections. Even the rather trivial mesh shown in \cgalFigureRef{SMS-selfintersection}
results in a self intersection when one edge is collapsed using the Lindstrom-Turk method.

\cgalFigureBegin{SMS-selfintersection, SMS-selfintersection.png}
Simple mesh before and after the collapse of edge `v-w` into vertex `w`. While the normals of `f1` and `f2` are almost equal, they are opposed after the edge collapse.
\cgalFigureEnd

The class `Surface_mesh_simplification::Bounded_normal_change_filter` checks if
a placement would invert the normal of a face around the stars of the
two vertices of an edge that is candidate for an edge collapse.  It then
rejects this placement by returning `boost::none`.

\note This filter class replaces the usage of the class `Surface_mesh_simplification::Bounded_normal_change_placement`.
Using the filter is faster as it is only performed on the edge to be collapsed next,
and not during the update of all edges incident to the vertex that is the result of the edge collapse.

\cgalExample{Surface_mesh_simplification/edge_collapse_bounded_normal_change.cpp}



\subsection Surface_mesh_simplificationExamplewithEnvelope Example with Polyhedral Envelope

The surface mesh simplification can be done in a way that the simplified mesh stays inside an envelope of the input mesh.
This makes use of the class `Polyhedral_envelope` which enables to check whether a query point, segment, or triangle lies within
a polyhedral envelope, which consists of the union of inflated triangles. While the user gives a tolerance \f$\epsilon\f$, the check
is conservative, that is there may be triangles which would be inside a surface obtained as the Minkowski sum envelope
with a sphere of radius \f$\epsilon\f$, but which are outside the polyhedral envelope.

\cgalExample{Surface_mesh_simplification/edge_collapse_envelope.cpp}



\subsection Surface_mesh_simplificationExamplewithVisitor Example with Visitor

The last example shows how to use a visitor with callbacks that are called at the different
steps of the simplification algorithm.

\cgalExample{Surface_mesh_simplification/edge_collapse_visitor_surface_mesh.cpp}

\subsection Surface_mesh_simplificationExampleWithGarlandHeckbertUsingAPolyhedron Example Using Garland-Heckbert Policies

Garland-Heckbert simplification policies are implemented with a single class,
`Surface_mesh_simplification::GarlandHeckbert_policies`, that regroups both a cost and
a placement policy class: this is because either of these policies requires the other one to function properly.
Note that it is still possible to wrap either policy: for example, it is possible to use
behavior modifiers such as `Surface_mesh_simplification::Bounded_normal_change_placement`
with the Garland-Heckbert placement policy.

\cgalExample{Surface_mesh_simplification/edge_collapse_garland_heckbert.cpp}

Note that these policies depend on the third party \ref thirdpartyEigen library.

\section SimplificationDesign Design and Implementation History

The core of the package, as well as most of the simplification strategies, are the work of Fernando Cacciola,
betwen 2006 and 2009.

Andreas Fabri added the `Bounded_normal_change_placement` functionality in \cgal 4.11.

The implementation of the Garland-Heckbert simplification policies is the result of the work
of Baskın Şenbaşlar during the 2019 season of the Google Summer of Code.
He was mentored by Mael Rouxel-Labbé, who also contributed to the code and to the documentation.

*/
} /* namespace CGAL */
