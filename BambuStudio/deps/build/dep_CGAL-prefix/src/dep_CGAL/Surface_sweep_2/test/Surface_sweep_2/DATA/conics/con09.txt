14
f 2 2  0 0
f 6 6  0 0
f 2 2  0 4
f 4 4  0 4
f 2 2  0 -4
f 4 4  0 -4
f 2 2  4 0
f 4 4  4 0
f 2 2  -4 0
f 4 4  -4 0
f 1 1  0 4
f 1 1  0 -4
f 1 1  4 0
f 1 1  -4 0
106
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 0} : (-4.5,-3.96863) --cw--> (-8,0)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -36} : (-4.5,-3.96863) --cw--> (-6,0)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -36} : (-6,0) --cw--> (-4.5,3.96863)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 0} : (-8,0) --cw--> (-4.5,3.96863)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 0} : (-4,-4) --cw--> (-4.5,-3.96863)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 0} : (-4.5,3.96863) --cw--> (-4,4)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -36} : (-3.96863,-4.5) --cw--> (-4.5,-3.96863)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 0} : (-3.96863,-4.5) --cw--> (-4,-4)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 0} : (-4,4) --cw--> (-3.96863,4.5)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -36} : (-4.5,3.96863) --cw--> (-3.96863,4.5)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 0} : (-4,-4) --cw--> (-3.41144,-1.91144)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 12} : (-3.41144,-1.91144) --cw--> (-6,0)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 12} : (-6,0) --cw--> (-3.41144,1.91144)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 0} : (-3.41144,1.91144) --cw--> (-4,4)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 15} : (-3,0) --cw--> (-5,0)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 15} : (-5,0) --cw--> (-3,0)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 12} : (-2.08856,-0.588562) --cw--> (-3.41144,-1.91144)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 0} : (-3.41144,-1.91144) --cw--> (-2.08856,-0.588562)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 0} : (-2.08856,0.588562) --cw--> (-3.41144,1.91144)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 12} : (-3.41144,1.91144) --cw--> (-2.08856,0.588562)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 12} : (-2,0) --cw--> (-2.08856,-0.588562)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 12} : (-2.08856,0.588562) --cw--> (-2,0)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 0} : (-2.08856,-0.588562) --cw--> (-1.93649,-0.5)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -4} : (-1.93649,-0.5) --cw--> (-2,0)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -4} : (-2,0) --cw--> (-1.93649,0.5)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 0} : (-1.93649,0.5) --cw--> (-2.08856,0.588562)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 12} : (-2,-4) --cw--> (-1.91144,-3.41144)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 0} : (-1.91144,-3.41144) --cw--> (-4,-4)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 0} : (-4,4) --cw--> (-1.91144,3.41144)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 12} : (-1.91144,3.41144) --cw--> (-2,4)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 0} : (-0.588562,-2.08856) --cw--> (-1.91144,-3.41144)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 12} : (-1.91144,-3.41144) --cw--> (-0.588562,-2.08856)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 12} : (-0.588562,2.08856) --cw--> (-1.91144,3.41144)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 0} : (-1.91144,3.41144) --cw--> (-0.588562,2.08856)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 0} : (-0.5,-1.93649) --cw--> (-0.588562,-2.08856)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -4} : (-0.5,-1.93649) --cw--> (-1.93649,-0.5)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -4} : (-1.93649,0.5) --cw--> (-0.5,1.93649)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 0} : (-0.588562,2.08856) --cw--> (-0.5,1.93649)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -36} : (0,-6) --cw--> (-3.96863,-4.5)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 12} : (0,-6) --cw--> (-2,-4)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 12} : (-0.588562,-2.08856) --cw--> (0,-2)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -4} : (0,-2) --cw--> (-0.5,-1.93649)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 0} : (0,0) --cw--> (-0.5,-1.93649)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 0} : (-1.93649,-0.5) --cw--> (0,0)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 0} : (0,0) --cw--> (-1.93649,0.5)
{1*x^2 + 1*y^2 + 0*xy + 8*x + 0*y + 0} : (-0.5,1.93649) --cw--> (0,0)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -4} : (-0.5,1.93649) --cw--> (0,2)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 12} : (0,2) --cw--> (-0.588562,2.08856)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 12} : (-2,4) --cw--> (0,6)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -36} : (-3.96863,4.5) --cw--> (0,6)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -4} : (0.5,-1.93649) --cw--> (0,-2)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 0} : (0.5,-1.93649) --cw--> (0,0)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 0} : (0,0) --cw--> (0.5,1.93649)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -4} : (0,2) --cw--> (0.5,1.93649)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 12} : (0,-2) --cw--> (0.588562,-2.08856)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 0} : (0.588562,-2.08856) --cw--> (0.5,-1.93649)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 0} : (0.5,1.93649) --cw--> (0.588562,2.08856)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 12} : (0.588562,2.08856) --cw--> (0,2)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 15} : (1,-4) --cw--> (-1,-4)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 15} : (-1,-4) --cw--> (1,-4)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 15} : (1,4) --cw--> (-1,4)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 15} : (-1,4) --cw--> (1,4)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 0} : (1.91144,-3.41144) --cw--> (0.588562,-2.08856)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 12} : (0.588562,-2.08856) --cw--> (1.91144,-3.41144)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 12} : (1.91144,3.41144) --cw--> (0.588562,2.08856)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 0} : (0.588562,2.08856) --cw--> (1.91144,3.41144)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -4} : (1.93649,-0.5) --cw--> (0.5,-1.93649)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 0} : (0,0) --cw--> (1.93649,-0.5)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 0} : (1.93649,0.5) --cw--> (0,0)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -4} : (0.5,1.93649) --cw--> (1.93649,0.5)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 12} : (2,-4) --cw--> (0,-6)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 12} : (1.91144,-3.41144) --cw--> (2,-4)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -4} : (2,0) --cw--> (1.93649,-0.5)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -4} : (1.93649,0.5) --cw--> (2,0)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 12} : (2,4) --cw--> (1.91144,3.41144)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 12} : (0,6) --cw--> (2,4)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 0} : (1.93649,-0.5) --cw--> (2.08856,-0.588562)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 12} : (2.08856,-0.588562) --cw--> (2,0)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 12} : (2,0) --cw--> (2.08856,0.588562)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 0} : (2.08856,0.588562) --cw--> (1.93649,0.5)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 12} : (3.41144,-1.91144) --cw--> (2.08856,-0.588562)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 0} : (2.08856,-0.588562) --cw--> (3.41144,-1.91144)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 0} : (3.41144,1.91144) --cw--> (2.08856,0.588562)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 12} : (2.08856,0.588562) --cw--> (3.41144,1.91144)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 0} : (3.96863,-4.5) --cw--> (-3.96863,-4.5)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -36} : (3.96863,-4.5) --cw--> (0,-6)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -36} : (0,6) --cw--> (3.96863,4.5)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 0} : (-3.96863,4.5) --cw--> (3.96863,4.5)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 0} : (4,-4) --cw--> (3.96863,-4.5)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 0} : (4,-4) --cw--> (1.91144,-3.41144)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 8*y + 0} : (3.41144,-1.91144) --cw--> (4,-4)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 0} : (4,4) --cw--> (3.41144,1.91144)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 0} : (1.91144,3.41144) --cw--> (4,4)
{1*x^2 + 1*y^2 + 0*xy + 0*x + -8*y + 0} : (3.96863,4.5) --cw--> (4,4)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -36} : (4.5,-3.96863) --cw--> (3.96863,-4.5)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 0} : (4.5,-3.96863) --cw--> (4,-4)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 0} : (4,4) --cw--> (4.5,3.96863)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -36} : (3.96863,4.5) --cw--> (4.5,3.96863)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 15} : (5,0) --cw--> (3,0)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 15} : (3,0) --cw--> (5,0)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -36} : (6,0) --cw--> (4.5,-3.96863)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 12} : (6,0) --cw--> (3.41144,-1.91144)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 12} : (3.41144,1.91144) --cw--> (6,0)
{1*x^2 + 1*y^2 + 0*xy + 0*x + 0*y + -36} : (4.5,3.96863) --cw--> (6,0)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 0} : (8,0) --cw--> (4.5,-3.96863)
{1*x^2 + 1*y^2 + 0*xy + -8*x + 0*y + 0} : (4.5,3.96863) --cw--> (8,0)
59
34

