/// \defgroup PkgSurfaceSweep2Ref 2D Intersection of Curves Reference
/*!
\addtogroup PkgSurfaceSweep2Ref
\todo check generated documentation
\cgalPkgDescriptionBegin{2D Intersection of Curves,PkgSurfaceSweep2}
\cgalPkgPicture{Curve_intersections_2.png}
\cgalPkgSummaryBegin
\cgalPkgAuthors{<PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>}
\cgalPkgDesc{This package provides three free functions implemented based on the sweep-line paradigm: given a collection of input curves, compute all intersection points, compute the set of subcurves that are pairwise interior-disjoint induced by them, and check whether there is at least one pair of curves among them that intersect in their interior.}
\cgalPkgManuals{Chapter_2D_Intersection_of_Curves,PkgSurfaceSweep2Ref}
\cgalPkgSummaryEnd
\cgalPkgShortInfoBegin
\cgalPkgSince{2.4}
\cgalPkgDependsOn{\ref PkgArrangementOnSurface2}
\cgalPkgBib{cgal:wfz-ic2}
\cgalPkgLicense{\ref licensesGPL "GPL"}
\cgalPkgShortInfoEnd
\cgalPkgDescriptionEnd

This chapter describes three functions implemented using the
sweep-line algorithm: given a collection \f$ {\mathcal C}\f$ of
planar curves, compute all intersection points among them,
obtain the set of maximal pairwise interior-disjoint subcurves
of the curves in \f$ {\mathcal C}\f$, or check whether there is at
least one pair of curves in \f$ {\mathcal C}\f$ that intersect in
their interior.

The first two operations are performed in an output-sensitive
manner.

\cgalClassifedRefPages

\cgalCRPSection{Functions}
- `CGAL::compute_intersection_points`
- `CGAL::compute_subcurves`
- `CGAL::do_curves_intersect`

*/

