/// \defgroup PkgSurfaceMeshParameterizationRef Triangulated Surface Mesh Parameterization Reference


/*!
\addtogroup PkgSurfaceMeshParameterizationRef
\cgalPkgDescriptionBegin{Triangulated Surface Mesh Parameterization,PkgSurfaceMeshParameterization}
\cgalPkgPicture{bimbaDetail.png}
\cgalPkgSummaryBegin
\cgalPkgAuthors{<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>}
\cgalPkgDesc{Parameterizing a surface amounts to finding a one-to-one mapping from
a suitable domain to the surface. In this package, we focus on triangulated surfaces
that are homeomorphic to a disk and on piecewise linear mappings into a planar domain.
This package implements several surface mesh parameterization methods, such as
As Rigid As Possible Parameterization, Discrete Authalic Parameterization,
Discrete Conformal Map, Floater Mean Value Coordinates, Least Squares Conformal Maps,
Orbifold Tutte Embedding, or Tutte Barycentric Mapping.
The code is generic and works with any model of the `FaceGraph` concept.}
\cgalPkgManuals{Chapter_Planar_Parameterization_of_Triangulated_Surface_Meshes, PkgSurfaceMeshParameterizationRef}
\cgalPkgSummaryEnd
\cgalPkgShortInfoBegin
\cgalPkgSince{3.2}
\cgalPkgDependsOn{\ref PkgSolverInterface}
\cgalPkgBib{cgal:salf-pptsm2}
\cgalPkgLicense{\ref licensesGPL "GPL"}
\cgalPkgDemo{Polyhedron demo,polyhedron_3.zip}
\cgalPkgShortInfoEnd
\cgalPkgDescriptionEnd

\cgalClassifedRefPages

\cgalCRPSection{Main Function}

- `CGAL::Surface_mesh_parameterization::parameterize()`

\cgalCRPSection{Concepts}

- `Parameterizer_3`

\cgalCRPSection{Surface Parameterization Methods}

This \cgal package implements several parameterization methods:

- Fixed border:
  - Tutte Barycentric Mapping \cgalCite{t-hdg-63} :
     One-to-one mapping is guaranteed for convex border.
  - Discrete Authalic Parameterization \cgalCite{cgal:dma-ipsm-02} :
     Conditionally guaranteed if all weights are positive and border is convex.
  - Discrete Conformal Map \cgalCite{cgal:eddhls-maam-95} :
     Conditionally guaranteed if all weights are positive and border is convex.
  - Floater Mean Value Coordinates \cgalCite{cgal:f-mvc-03} :
     One-to-one mapping is guaranteed for convex border.
  - Iterative Authalic Parameterization \cgalCite{cgal:j-lrsspp-19}.
- Free border:
  - As Rigid As Possible Parameterization \cgalCite{liu2008local}
  - Least Squares Conformal Maps (LSCM) \cgalCite{cgal:lprm-lscm-02}.
- Borderless:
  - Orbifold Tutte Embeddings \cgalCite{aigerman2015orbifold}.

The following classes implement the methods listed above:
- `CGAL::Surface_mesh_parameterization::ARAP_parameterizer_3<TriangleMesh, BorderParameterizer, SolverTraits>`
- `CGAL::Surface_mesh_parameterization::Barycentric_mapping_parameterizer_3<TriangleMesh, BorderParameterizer, SolverTraits>`
- `CGAL::Surface_mesh_parameterization::Discrete_authalic_parameterizer_3<TriangleMesh, BorderParameterizer, SolverTraits>`
- `CGAL::Surface_mesh_parameterization::Discrete_conformal_map_parameterizer_3<TriangleMesh, BorderParameterizer, SolverTraits>`
- `CGAL::Surface_mesh_parameterization::Iterative_authalic_parameterizer_3<TriangleMesh, BorderParameterizer, SolverTraits>`
- `CGAL::Surface_mesh_parameterization::LSCM_parameterizer_3<TriangleMesh, BorderParameterizer>`
- `CGAL::Surface_mesh_parameterization::Mean_value_coordinates_parameterizer_3<TriangleMesh, BorderParameterizer, SolverTraits>`
- `CGAL::Surface_mesh_parameterization::Orbifold_Tutte_parameterizer_3<TriangleMesh, SolverTraits>`

\cgalCRPSection{Border Parameterization Methods}

Border parameterization methods define a
set of constraints (a constraint specifies two (u,v) coordinates for
each instance of a vertex along the border).

This package implements common border parameterization methods:
- For fixed border methods:
   - the user can select a border parameterization among two common methods:
     uniform or arc-length parameterizations.
   - the user can select a convex shape among a set of standard ones (circle, square).
- For free border methods: at least two constraints (the pinned vertices).

The following classes implement the methods listed above:
- `CGAL::Surface_mesh_parameterization::Circular_border_uniform_parameterizer_3<TriangleMesh>`
- `CGAL::Surface_mesh_parameterization::Circular_border_arc_length_parameterizer_3<TriangleMesh>`
- `CGAL::Surface_mesh_parameterization::Fixed_border_parameterizer_3<TriangleMesh, BorderParameterizer, SolverTraits>`
- `CGAL::Surface_mesh_parameterization::Square_border_uniform_parameterizer_3<TriangleMesh>`
- `CGAL::Surface_mesh_parameterization::Square_border_arc_length_parameterizer_3<TriangleMesh>`
- `CGAL::Surface_mesh_parameterization::Two_vertices_parameterizer_3<TriangleMesh>`

\cgalCRPSection{Mesh}

The general definition of input meshes handled directly by
`CGAL::Surface_mesh_parameterization::parameterize()` is a triangulated surface
mesh model of `FaceGraph` that is homeomorphic to a disc (may have holes). In
particular, it means that it must be 2-manifold and oriented.

\cgalCRPSection{Checks and Assertions}

The package performs the next checks:

- For <i>fixed</i> border parameterizations:
 - Preconditions:
    - the border is mapped onto a convex polygon.
    - the input mesh is triangular.
    - the input mesh is a surface with one connected component.

- For <i>free</i> border parameterizations:
 - Preconditions:
    - the input mesh is triangular.
    - the input mesh is a surface with one connected component.
- For <i>borderless</i> parameterizations:
 - Preconditions:
    - the input mesh is triangular.
    - the input mesh is a surface with one connected component.
*/

/*!
 \defgroup PkgSurfaceMeshParameterizationMainFunction Main Functions
 \ingroup PkgSurfaceMeshParameterizationRef

 The central functions, main entry point of the package.
*/

/*!
 \defgroup PkgSurfaceMeshParameterizationEnums Enums
 \ingroup PkgSurfaceMeshParameterizationRef

 The enum classes defined and used in this package.
*/

/*!
 \defgroup PkgSurfaceMeshParameterizationConcepts Concepts
 \ingroup PkgSurfaceMeshParameterizationRef

 The concepts of this package.
*/

/*!
 \defgroup PkgSurfaceMeshParameterizationMethods Surface Parameterization Methods
 \ingroup PkgSurfaceMeshParameterizationRef

 This \cgal package implements several parameterization methods:

 - Fixed border:
  - Tutte Barycentric Mapping \cgalCite{t-hdg-63}.
     One-to-one mapping is guaranteed for convex border.
  - Floater Mean Value Coordinates \cgalCite{cgal:f-mvc-03}.
     One-to-one mapping is guaranteed for convex border.
  - Discrete Conformal Map \cgalCite{cgal:eddhls-maam-95}.
    Conditionally guaranteed if all weights are positive and border is convex.
  - Discrete Authalic parameterization \cgalCite{cgal:dma-ipsm-02}.
    Conditionally guaranteed if all weights are positive and border is convex.
 - Free border:
  - As Rigid As Possible Maps \cgalCite{liu2008local}
  - Least Squares Conformal Maps \cgalCite{cgal:lprm-lscm-02}.
 - Borderless:
  - Orbifold Tutte Embeddings \cgalCite{aigerman2015orbifold}
*/

/*!
 \defgroup PkgSurfaceMeshParameterizationBorderParameterizationMethods Border Parameterization Methods
 \ingroup PkgSurfaceMeshParameterizationRef

Border parameterization methods define a
set of constraints (a constraint specifies two (u,v) coordinates for
each instance of a vertex along the border).

This package implements common border parameterization methods:
 - For fixed border methods:
   - the user can select a border
      parameterization among two common methods: uniform or
      arc-length parameterizations.
   - one convex shape specified by:
    - one shape among a set of standard ones (circle, square).
 - For free border methods: at least two constraints (the pinned
vertices).
*/

/*!
 \defgroup PkgSurfaceMeshParameterizationOrbifoldHelperFunctions Orbifold Helper Functions
 \ingroup PkgSurfaceMeshParameterizationMethods


*/

