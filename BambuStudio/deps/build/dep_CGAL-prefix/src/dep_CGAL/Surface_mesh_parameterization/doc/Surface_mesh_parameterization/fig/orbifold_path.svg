<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="908pt" height="272pt" viewBox="0 0 454 136" version="1.1">
<g id="surface1">
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187.643005 687.919155 L 198.615661 717.176968 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 169.357849 720.833218 L 136.443786 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 198.615661 717.176968 L 231.529724 717.176968 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 136.443786 720.833218 L 151.072692 764.719937 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 118.15863 742.77853 L 136.443786 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 151.072692 764.719937 L 165.701599 804.950405 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 220.557067 775.692593 L 216.900817 804.950405 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 183.986755 793.977749 L 220.557067 775.692593 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 151.072692 764.719937 L 169.357849 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 88.900817 768.376187 L 118.15863 801.294155 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 81.584411 793.977749 L 118.15863 801.294155 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 88.900817 768.376187 L 81.584411 793.977749 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 52.326599 764.719937 L 81.584411 793.977749 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 118.15863 801.294155 L 121.81488 768.376187 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 151.072692 764.719937 L 118.15863 801.294155 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 88.900817 768.376187 L 52.326599 764.719937 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 88.900817 731.805874 L 52.326599 764.719937 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 52.326599 720.833218 L 52.326599 764.719937 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 88.900817 731.805874 L 118.15863 742.77853 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 88.900817 768.376187 L 88.900817 731.805874 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 99.869567 676.946499 L 132.787536 676.946499 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 136.443786 720.833218 L 99.869567 676.946499 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 132.787536 676.946499 L 136.443786 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 99.869567 676.946499 L 85.240661 706.204312 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 52.326599 720.833218 L 99.869567 676.946499 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 88.900817 768.376187 L 118.15863 742.77853 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 121.81488 768.376187 L 88.900817 768.376187 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 52.326599 720.833218 L 85.240661 706.204312 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 88.900817 731.805874 L 52.326599 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 169.357849 720.833218 L 198.615661 717.176968 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187.643005 750.09103 L 169.357849 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 198.615661 717.176968 L 187.643005 750.09103 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 85.240661 706.204312 L 136.443786 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 88.900817 731.805874 L 85.240661 706.204312 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 136.443786 720.833218 L 88.900817 731.805874 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187.643005 687.919155 L 169.357849 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 132.787536 676.946499 L 187.643005 687.919155 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 169.357849 720.833218 L 132.787536 676.946499 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187.643005 750.09103 L 231.529724 717.176968 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 220.557067 775.692593 L 187.643005 750.09103 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 231.529724 717.176968 L 220.557067 775.692593 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 183.986755 793.977749 L 151.072692 764.719937 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 187.643005 750.09103 L 183.986755 793.977749 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 151.072692 764.719937 L 187.643005 750.09103 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 118.15863 801.294155 L 165.701599 804.950405 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 231.529724 717.176968 L 187.643005 687.919155 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 151.072692 764.719937 L 121.81488 768.376187 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 118.15863 742.77853 L 151.072692 764.719937 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 121.81488 768.376187 L 118.15863 742.77853 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 216.900817 804.950405 L 165.701599 804.950405 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 183.986755 793.977749 L 216.900817 804.950405 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 165.701599 804.950405 L 183.986755 793.977749 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 35.457031 14.671875 C 35.457031 11.339844 30.457031 11.339844 30.457031 14.671875 C 30.457031 18.003906 35.457031 18.003906 35.457031 14.671875 Z M 35.457031 14.671875 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 35.957031 14.671875 C 35.957031 10.671875 29.957031 10.671875 29.957031 14.671875 C 29.957031 18.671875 35.957031 18.671875 35.957031 14.671875 Z M 34.957031 14.671875 C 34.957031 12.003906 30.957031 12.003906 30.957031 14.671875 C 30.957031 17.339844 34.957031 17.339844 34.957031 14.671875 Z M 34.957031 14.671875 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 72.03125 7.355469 C 72.03125 4.023438 67.03125 4.023438 67.03125 7.355469 C 67.03125 10.691406 72.03125 10.691406 72.03125 7.355469 Z M 72.03125 7.355469 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 72.53125 7.355469 C 72.53125 3.355469 66.53125 3.355469 66.53125 7.355469 C 66.53125 11.355469 72.53125 11.355469 72.53125 7.355469 Z M 71.53125 7.355469 C 71.53125 4.691406 67.53125 4.691406 67.53125 7.355469 C 67.53125 10.023438 71.53125 10.023438 71.53125 7.355469 Z M 71.53125 7.355469 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 119.574219 3.699219 C 119.574219 0.367188 114.574219 0.367188 114.574219 3.699219 C 114.574219 7.035156 119.574219 7.035156 119.574219 3.699219 Z M 119.574219 3.699219 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 120.074219 3.699219 C 120.074219 -0.300781 114.074219 -0.300781 114.074219 3.699219 C 114.074219 7.699219 120.074219 7.699219 120.074219 3.699219 Z M 119.074219 3.699219 C 119.074219 1.035156 115.074219 1.035156 115.074219 3.699219 C 115.074219 6.367188 119.074219 6.367188 119.074219 3.699219 Z M 119.074219 3.699219 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 170.773438 3.699219 C 170.773438 0.367188 165.773438 0.367188 165.773438 3.699219 C 165.773438 7.035156 170.773438 7.035156 170.773438 3.699219 Z M 170.773438 3.699219 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 171.273438 3.699219 C 171.273438 -0.300781 165.273438 -0.300781 165.273438 3.699219 C 165.273438 7.699219 171.273438 7.699219 171.273438 3.699219 Z M 170.273438 3.699219 C 170.273438 1.035156 166.273438 1.035156 166.273438 3.699219 C 166.273438 6.367188 170.273438 6.367188 170.273438 3.699219 Z M 170.273438 3.699219 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 152.488281 91.472656 C 152.488281 88.140625 147.488281 88.140625 147.488281 91.472656 C 147.488281 94.804688 152.488281 94.804688 152.488281 91.472656 Z M 152.488281 91.472656 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 152.988281 91.472656 C 152.988281 87.472656 146.988281 87.472656 146.988281 91.472656 C 146.988281 95.472656 152.988281 95.472656 152.988281 91.472656 Z M 151.988281 91.472656 C 151.988281 88.804688 147.988281 88.804688 147.988281 91.472656 C 147.988281 94.140625 151.988281 94.140625 151.988281 91.472656 Z M 151.988281 91.472656 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 185.402344 91.472656 C 185.402344 88.140625 180.402344 88.140625 180.402344 91.472656 C 180.402344 94.804688 185.402344 94.804688 185.402344 91.472656 Z M 185.402344 91.472656 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 185.902344 91.472656 C 185.902344 87.472656 179.902344 87.472656 179.902344 91.472656 C 179.902344 95.472656 185.902344 95.472656 185.902344 91.472656 Z M 184.902344 91.472656 C 184.902344 88.804688 180.902344 88.804688 180.902344 91.472656 C 180.902344 94.140625 184.902344 94.140625 184.902344 91.472656 Z M 184.902344 91.472656 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 123.230469 87.816406 C 123.230469 84.484375 118.230469 84.484375 118.230469 87.816406 C 118.230469 91.148438 123.230469 91.148438 123.230469 87.816406 Z M 123.230469 87.816406 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 123.730469 87.816406 C 123.730469 83.816406 117.730469 83.816406 117.730469 87.816406 C 117.730469 91.816406 123.730469 91.816406 123.730469 87.816406 Z M 122.730469 87.816406 C 122.730469 85.148438 118.730469 85.148438 118.730469 87.816406 C 118.730469 90.484375 122.730469 90.484375 122.730469 87.816406 Z M 122.730469 87.816406 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 141.515625 120.730469 C 141.515625 117.398438 136.515625 117.398438 136.515625 120.730469 C 136.515625 124.0625 141.515625 124.0625 141.515625 120.730469 Z M 141.515625 120.730469 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 142.015625 120.730469 C 142.015625 116.730469 136.015625 116.730469 136.015625 120.730469 C 136.015625 124.730469 142.015625 124.730469 142.015625 120.730469 Z M 141.015625 120.730469 C 141.015625 118.0625 137.015625 118.0625 137.015625 120.730469 C 137.015625 123.398438 141.015625 123.398438 141.015625 120.730469 Z M 141.015625 120.730469 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 75.6875 40.273438 C 75.6875 36.9375 70.6875 36.9375 70.6875 40.273438 C 70.6875 43.605469 75.6875 43.605469 75.6875 40.273438 Z M 75.6875 40.273438 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 76.1875 40.273438 C 76.1875 36.273438 70.1875 36.273438 70.1875 40.273438 C 70.1875 44.273438 76.1875 44.273438 76.1875 40.273438 Z M 75.1875 40.273438 C 75.1875 37.605469 71.1875 37.605469 71.1875 40.273438 C 71.1875 42.9375 75.1875 42.9375 75.1875 40.273438 Z M 75.1875 40.273438 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 42.773438 40.273438 C 42.773438 36.9375 37.773438 36.9375 37.773438 40.273438 C 37.773438 43.605469 42.773438 43.605469 42.773438 40.273438 Z M 42.773438 40.273438 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 43.273438 40.273438 C 43.273438 36.273438 37.273438 36.273438 37.273438 40.273438 C 37.273438 44.273438 43.273438 44.273438 43.273438 40.273438 Z M 42.273438 40.273438 C 42.273438 37.605469 38.273438 37.605469 38.273438 40.273438 C 38.273438 42.9375 42.273438 42.9375 42.273438 40.273438 Z M 42.273438 40.273438 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 6.199219 87.816406 C 6.199219 84.484375 1.199219 84.484375 1.199219 87.816406 C 1.199219 91.148438 6.199219 91.148438 6.199219 87.816406 Z M 6.199219 87.816406 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 6.699219 87.816406 C 6.699219 83.816406 0.699219 83.816406 0.699219 87.816406 C 0.699219 91.816406 6.699219 91.816406 6.699219 87.816406 Z M 5.699219 87.816406 C 5.699219 85.148438 1.699219 85.148438 1.699219 87.816406 C 1.699219 90.484375 5.699219 90.484375 5.699219 87.816406 Z M 5.699219 87.816406 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 39.113281 102.445312 C 39.113281 99.109375 34.113281 99.109375 34.113281 102.445312 C 34.113281 105.777344 39.113281 105.777344 39.113281 102.445312 Z M 39.113281 102.445312 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 39.613281 102.445312 C 39.613281 98.445312 33.613281 98.445312 33.613281 102.445312 C 33.613281 106.445312 39.613281 106.445312 39.613281 102.445312 Z M 38.613281 102.445312 C 38.613281 99.777344 34.613281 99.777344 34.613281 102.445312 C 34.613281 105.109375 38.613281 105.109375 38.613281 102.445312 Z M 38.613281 102.445312 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 53.742188 131.703125 C 53.742188 128.367188 48.742188 128.367188 48.742188 131.703125 C 48.742188 135.035156 53.742188 135.035156 53.742188 131.703125 Z M 53.742188 131.703125 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 54.242188 131.703125 C 54.242188 127.703125 48.242188 127.703125 48.242188 131.703125 C 48.242188 135.703125 54.242188 135.703125 54.242188 131.703125 Z M 53.242188 131.703125 C 53.242188 129.035156 49.242188 129.035156 49.242188 131.703125 C 49.242188 134.367188 53.242188 134.367188 53.242188 131.703125 Z M 53.242188 131.703125 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 86.660156 131.703125 C 86.660156 128.367188 81.660156 128.367188 81.660156 131.703125 C 81.660156 135.035156 86.660156 135.035156 86.660156 131.703125 Z M 86.660156 131.703125 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 87.160156 131.703125 C 87.160156 127.703125 81.160156 127.703125 81.160156 131.703125 C 81.160156 135.703125 87.160156 135.703125 87.160156 131.703125 Z M 86.160156 131.703125 C 86.160156 129.035156 82.160156 129.035156 82.160156 131.703125 C 82.160156 134.367188 86.160156 134.367188 86.160156 131.703125 Z M 86.160156 131.703125 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 6.199219 43.929688 C 6.199219 40.597656 1.199219 40.597656 1.199219 43.929688 C 1.199219 47.261719 6.199219 47.261719 6.199219 43.929688 Z M 6.199219 43.929688 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 6.699219 43.929688 C 6.699219 39.929688 0.699219 39.929688 0.699219 43.929688 C 0.699219 47.929688 6.699219 47.929688 6.699219 43.929688 Z M 5.699219 43.929688 C 5.699219 41.261719 1.699219 41.261719 1.699219 43.929688 C 1.699219 46.597656 5.699219 46.597656 5.699219 43.929688 Z M 5.699219 43.929688 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 174.429688 32.957031 C 174.429688 29.625 169.429688 29.625 169.429688 32.957031 C 169.429688 36.289062 174.429688 36.289062 174.429688 32.957031 Z M 174.429688 32.957031 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 174.929688 32.957031 C 174.929688 28.957031 168.929688 28.957031 168.929688 32.957031 C 168.929688 36.957031 174.929688 36.957031 174.929688 32.957031 Z M 173.929688 32.957031 C 173.929688 30.289062 169.929688 30.289062 169.929688 32.957031 C 169.929688 35.625 173.929688 35.625 173.929688 32.957031 Z M 173.929688 32.957031 "/>
<path style="fill:none;stroke-width:0.8;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,75%,75%);stroke-opacity:1;stroke-miterlimit:10;" d="M 88.900817 731.805874 L 118.15863 742.77853 L 151.072692 764.719937 L 136.443786 720.833218 L 151.072692 764.719937 L 187.643005 750.09103 L 183.986755 793.977749 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 104.945312 43.929688 C 104.945312 40.597656 99.945312 40.597656 99.945312 43.929688 C 99.945312 47.261719 104.945312 47.261719 104.945312 43.929688 Z M 104.945312 43.929688 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 105.445312 43.929688 C 105.445312 39.929688 99.445312 39.929688 99.445312 43.929688 C 99.445312 47.929688 105.445312 47.929688 105.445312 43.929688 Z M 104.445312 43.929688 C 104.445312 41.261719 100.445312 41.261719 100.445312 43.929688 C 100.445312 46.597656 104.445312 46.597656 104.445312 43.929688 Z M 104.445312 43.929688 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 141.515625 58.558594 C 141.515625 55.226562 136.515625 55.226562 136.515625 58.558594 C 136.515625 61.890625 141.515625 61.890625 141.515625 58.558594 Z M 141.515625 58.558594 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 142.015625 58.558594 C 142.015625 54.558594 136.015625 54.558594 136.015625 58.558594 C 136.015625 62.558594 142.015625 62.558594 142.015625 58.558594 Z M 141.015625 58.558594 C 141.015625 55.890625 137.015625 55.890625 137.015625 58.558594 C 137.015625 61.226562 141.015625 61.226562 141.015625 58.558594 Z M 141.015625 58.558594 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(93.3%,89%,45.1%);fill-opacity:1;" d="M 137.859375 14.671875 C 137.859375 11.339844 132.859375 11.339844 132.859375 14.671875 C 132.859375 18.003906 137.859375 18.003906 137.859375 14.671875 Z M 137.859375 14.671875 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 138.359375 14.671875 C 138.359375 10.671875 132.359375 10.671875 132.359375 14.671875 C 132.359375 18.671875 138.359375 18.671875 138.359375 14.671875 Z M 137.359375 14.671875 C 137.359375 12.003906 133.359375 12.003906 133.359375 14.671875 C 133.359375 17.339844 137.359375 17.339844 137.359375 14.671875 Z M 137.359375 14.671875 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(54.5%,71.8%,91.8%);fill-opacity:1;" d="M 90.316406 87.816406 C 90.316406 84.484375 85.316406 84.484375 85.316406 87.816406 C 85.316406 91.148438 90.316406 91.148438 90.316406 87.816406 Z M 90.316406 87.816406 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 90.816406 87.816406 C 90.816406 83.816406 84.816406 83.816406 84.816406 87.816406 C 84.816406 91.816406 90.816406 91.816406 90.816406 87.816406 Z M 89.816406 87.816406 C 89.816406 85.148438 85.816406 85.148438 85.816406 87.816406 C 85.816406 90.484375 89.816406 90.484375 89.816406 87.816406 Z M 89.816406 87.816406 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 72.03125 65.871094 C 72.03125 62.539062 67.03125 62.539062 67.03125 65.871094 C 67.03125 69.207031 72.03125 69.207031 72.03125 65.871094 Z M 72.03125 65.871094 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 72.53125 65.871094 C 72.53125 61.871094 66.53125 61.871094 66.53125 65.871094 C 66.53125 69.871094 72.53125 69.871094 72.53125 65.871094 Z M 71.53125 65.871094 C 71.53125 63.207031 67.53125 63.207031 67.53125 65.871094 C 67.53125 68.539062 71.53125 68.539062 71.53125 65.871094 Z M 71.53125 65.871094 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(93.3%,89%,45.1%);fill-opacity:1;" d="M 42.773438 76.84375 C 42.773438 73.511719 37.773438 73.511719 37.773438 76.84375 C 37.773438 80.175781 42.773438 80.175781 42.773438 76.84375 Z M 42.773438 76.84375 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 43.273438 76.84375 C 43.273438 72.84375 37.273438 72.84375 37.273438 76.84375 C 37.273438 80.84375 43.273438 80.84375 43.273438 76.84375 Z M 42.273438 76.84375 C 42.273438 74.175781 38.273438 74.175781 38.273438 76.84375 C 38.273438 79.511719 42.273438 79.511719 42.273438 76.84375 Z M 42.273438 76.84375 "/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 454.619568 687.919155 L 465.592224 717.176968 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 436.334411 720.833218 L 403.420349 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 465.592224 717.176968 L 498.506286 717.176968 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 403.420349 720.833218 L 418.049255 764.719937 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 385.135193 742.77853 L 403.420349 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 418.049255 764.719937 L 432.678161 804.950405 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 487.537536 775.692593 L 483.87738 804.950405 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 450.963318 793.977749 L 487.537536 775.692593 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 418.049255 764.719937 L 436.334411 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 355.87738 768.376187 L 385.135193 801.294155 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 348.560974 793.977749 L 385.135193 801.294155 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 355.87738 768.376187 L 348.560974 793.977749 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 319.303161 764.719937 L 348.560974 793.977749 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 385.135193 801.294155 L 388.791443 768.376187 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 418.049255 764.719937 L 385.135193 801.294155 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 355.87738 768.376187 L 319.303161 764.719937 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 355.87738 731.805874 L 319.303161 764.719937 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 319.303161 720.833218 L 319.303161 764.719937 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 355.87738 731.805874 L 385.135193 742.77853 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 355.87738 768.376187 L 355.87738 731.805874 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 366.84613 676.946499 L 399.764099 676.946499 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 403.420349 720.833218 L 366.84613 676.946499 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 399.764099 676.946499 L 403.420349 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 366.84613 676.946499 L 352.217224 706.204312 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 319.303161 720.833218 L 366.84613 676.946499 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 355.87738 768.376187 L 385.135193 742.77853 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 388.791443 768.376187 L 355.87738 768.376187 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 319.303161 720.833218 L 352.217224 706.204312 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 355.87738 731.805874 L 319.303161 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 436.334411 720.833218 L 465.592224 717.176968 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 454.619568 750.09103 L 436.334411 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 465.592224 717.176968 L 454.619568 750.09103 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 352.217224 706.204312 L 403.420349 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 403.420349 720.833218 L 355.87738 731.805874 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 454.619568 687.919155 L 436.334411 720.833218 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 399.764099 676.946499 L 454.619568 687.919155 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 436.334411 720.833218 L 399.764099 676.946499 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 454.619568 750.09103 L 498.506286 717.176968 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 487.537536 775.692593 L 454.619568 750.09103 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 498.506286 717.176968 L 487.537536 775.692593 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 450.963318 793.977749 L 418.049255 764.719937 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 454.619568 750.09103 L 450.963318 793.977749 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 418.049255 764.719937 L 454.619568 750.09103 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 385.135193 801.294155 L 432.678161 804.950405 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 498.506286 717.176968 L 454.619568 687.919155 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 418.049255 764.719937 L 388.791443 768.376187 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 385.135193 742.77853 L 418.049255 764.719937 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 388.791443 768.376187 L 385.135193 742.77853 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 483.87738 804.950405 L 432.678161 804.950405 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 450.963318 793.977749 L 483.87738 804.950405 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 432.678161 804.950405 L 450.963318 793.977749 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 302.433594 14.671875 C 302.433594 11.339844 297.433594 11.339844 297.433594 14.671875 C 297.433594 18.003906 302.433594 18.003906 302.433594 14.671875 Z M 302.433594 14.671875 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 302.933594 14.671875 C 302.933594 10.671875 296.933594 10.671875 296.933594 14.671875 C 296.933594 18.671875 302.933594 18.671875 302.933594 14.671875 Z M 301.933594 14.671875 C 301.933594 12.003906 297.933594 12.003906 297.933594 14.671875 C 297.933594 17.339844 301.933594 17.339844 301.933594 14.671875 Z M 301.933594 14.671875 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 339.007812 7.355469 C 339.007812 4.023438 334.007812 4.023438 334.007812 7.355469 C 334.007812 10.691406 339.007812 10.691406 339.007812 7.355469 Z M 339.007812 7.355469 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 339.507812 7.355469 C 339.507812 3.355469 333.507812 3.355469 333.507812 7.355469 C 333.507812 11.355469 339.507812 11.355469 339.507812 7.355469 Z M 338.507812 7.355469 C 338.507812 4.691406 334.507812 4.691406 334.507812 7.355469 C 334.507812 10.023438 338.507812 10.023438 338.507812 7.355469 Z M 338.507812 7.355469 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 386.550781 3.699219 C 386.550781 0.367188 381.550781 0.367188 381.550781 3.699219 C 381.550781 7.035156 386.550781 7.035156 386.550781 3.699219 Z M 386.550781 3.699219 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 387.050781 3.699219 C 387.050781 -0.300781 381.050781 -0.300781 381.050781 3.699219 C 381.050781 7.699219 387.050781 7.699219 387.050781 3.699219 Z M 386.050781 3.699219 C 386.050781 1.035156 382.050781 1.035156 382.050781 3.699219 C 382.050781 6.367188 386.050781 6.367188 386.050781 3.699219 Z M 386.050781 3.699219 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 437.75 3.699219 C 437.75 0.367188 432.75 0.367188 432.75 3.699219 C 432.75 7.035156 437.75 7.035156 437.75 3.699219 Z M 437.75 3.699219 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 438.25 3.699219 C 438.25 -0.300781 432.25 -0.300781 432.25 3.699219 C 432.25 7.699219 438.25 7.699219 438.25 3.699219 Z M 437.25 3.699219 C 437.25 1.035156 433.25 1.035156 433.25 3.699219 C 433.25 6.367188 437.25 6.367188 437.25 3.699219 Z M 437.25 3.699219 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 419.464844 91.472656 C 419.464844 88.140625 414.464844 88.140625 414.464844 91.472656 C 414.464844 94.804688 419.464844 94.804688 419.464844 91.472656 Z M 419.464844 91.472656 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 419.964844 91.472656 C 419.964844 87.472656 413.964844 87.472656 413.964844 91.472656 C 413.964844 95.472656 419.964844 95.472656 419.964844 91.472656 Z M 418.964844 91.472656 C 418.964844 88.804688 414.964844 88.804688 414.964844 91.472656 C 414.964844 94.140625 418.964844 94.140625 418.964844 91.472656 Z M 418.964844 91.472656 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 452.378906 91.472656 C 452.378906 88.140625 447.378906 88.140625 447.378906 91.472656 C 447.378906 94.804688 452.378906 94.804688 452.378906 91.472656 Z M 452.378906 91.472656 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 452.878906 91.472656 C 452.878906 87.472656 446.878906 87.472656 446.878906 91.472656 C 446.878906 95.472656 452.878906 95.472656 452.878906 91.472656 Z M 451.878906 91.472656 C 451.878906 88.804688 447.878906 88.804688 447.878906 91.472656 C 447.878906 94.140625 451.878906 94.140625 451.878906 91.472656 Z M 451.878906 91.472656 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 390.207031 87.816406 C 390.207031 84.484375 385.207031 84.484375 385.207031 87.816406 C 385.207031 91.148438 390.207031 91.148438 390.207031 87.816406 Z M 390.207031 87.816406 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 390.707031 87.816406 C 390.707031 83.816406 384.707031 83.816406 384.707031 87.816406 C 384.707031 91.816406 390.707031 91.816406 390.707031 87.816406 Z M 389.707031 87.816406 C 389.707031 85.148438 385.707031 85.148438 385.707031 87.816406 C 385.707031 90.484375 389.707031 90.484375 389.707031 87.816406 Z M 389.707031 87.816406 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 408.492188 120.730469 C 408.492188 117.398438 403.492188 117.398438 403.492188 120.730469 C 403.492188 124.0625 408.492188 124.0625 408.492188 120.730469 Z M 408.492188 120.730469 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 408.992188 120.730469 C 408.992188 116.730469 402.992188 116.730469 402.992188 120.730469 C 402.992188 124.730469 408.992188 124.730469 408.992188 120.730469 Z M 407.992188 120.730469 C 407.992188 118.0625 403.992188 118.0625 403.992188 120.730469 C 403.992188 123.398438 407.992188 123.398438 407.992188 120.730469 Z M 407.992188 120.730469 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 342.664062 40.273438 C 342.664062 36.9375 337.664062 36.9375 337.664062 40.273438 C 337.664062 43.605469 342.664062 43.605469 342.664062 40.273438 Z M 342.664062 40.273438 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 343.164062 40.273438 C 343.164062 36.273438 337.164062 36.273438 337.164062 40.273438 C 337.164062 44.273438 343.164062 44.273438 343.164062 40.273438 Z M 342.164062 40.273438 C 342.164062 37.605469 338.164062 37.605469 338.164062 40.273438 C 338.164062 42.9375 342.164062 42.9375 342.164062 40.273438 Z M 342.164062 40.273438 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 309.75 40.273438 C 309.75 36.9375 304.75 36.9375 304.75 40.273438 C 304.75 43.605469 309.75 43.605469 309.75 40.273438 Z M 309.75 40.273438 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 310.25 40.273438 C 310.25 36.273438 304.25 36.273438 304.25 40.273438 C 304.25 44.273438 310.25 44.273438 310.25 40.273438 Z M 309.25 40.273438 C 309.25 37.605469 305.25 37.605469 305.25 40.273438 C 305.25 42.9375 309.25 42.9375 309.25 40.273438 Z M 309.25 40.273438 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 273.175781 87.816406 C 273.175781 84.484375 268.175781 84.484375 268.175781 87.816406 C 268.175781 91.148438 273.175781 91.148438 273.175781 87.816406 Z M 273.175781 87.816406 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 273.675781 87.816406 C 273.675781 83.816406 267.675781 83.816406 267.675781 87.816406 C 267.675781 91.816406 273.675781 91.816406 273.675781 87.816406 Z M 272.675781 87.816406 C 272.675781 85.148438 268.675781 85.148438 268.675781 87.816406 C 268.675781 90.484375 272.675781 90.484375 272.675781 87.816406 Z M 272.675781 87.816406 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 320.71875 131.703125 C 320.71875 128.367188 315.71875 128.367188 315.71875 131.703125 C 315.71875 135.035156 320.71875 135.035156 320.71875 131.703125 Z M 320.71875 131.703125 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 321.21875 131.703125 C 321.21875 127.703125 315.21875 127.703125 315.21875 131.703125 C 315.21875 135.703125 321.21875 135.703125 321.21875 131.703125 Z M 320.21875 131.703125 C 320.21875 129.035156 316.21875 129.035156 316.21875 131.703125 C 316.21875 134.367188 320.21875 134.367188 320.21875 131.703125 Z M 320.21875 131.703125 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 353.636719 131.703125 C 353.636719 128.367188 348.636719 128.367188 348.636719 131.703125 C 348.636719 135.035156 353.636719 135.035156 353.636719 131.703125 Z M 353.636719 131.703125 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 354.136719 131.703125 C 354.136719 127.703125 348.136719 127.703125 348.136719 131.703125 C 348.136719 135.703125 354.136719 135.703125 354.136719 131.703125 Z M 353.136719 131.703125 C 353.136719 129.035156 349.136719 129.035156 349.136719 131.703125 C 349.136719 134.367188 353.136719 134.367188 353.136719 131.703125 Z M 353.136719 131.703125 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 273.175781 43.929688 C 273.175781 40.597656 268.175781 40.597656 268.175781 43.929688 C 268.175781 47.261719 273.175781 47.261719 273.175781 43.929688 Z M 273.175781 43.929688 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 273.675781 43.929688 C 273.675781 39.929688 267.675781 39.929688 267.675781 43.929688 C 267.675781 47.929688 273.675781 47.929688 273.675781 43.929688 Z M 272.675781 43.929688 C 272.675781 41.261719 268.675781 41.261719 268.675781 43.929688 C 268.675781 46.597656 272.675781 46.597656 272.675781 43.929688 Z M 272.675781 43.929688 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 441.410156 32.957031 C 441.410156 29.625 436.410156 29.625 436.410156 32.957031 C 436.410156 36.289062 441.410156 36.289062 441.410156 32.957031 Z M 441.410156 32.957031 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 441.910156 32.957031 C 441.910156 28.957031 435.910156 28.957031 435.910156 32.957031 C 435.910156 36.957031 441.910156 36.957031 441.910156 32.957031 Z M 440.910156 32.957031 C 440.910156 30.289062 436.910156 30.289062 436.910156 32.957031 C 436.910156 35.625 440.910156 35.625 440.910156 32.957031 Z M 440.910156 32.957031 "/>
<path style="fill:none;stroke-width:0.8;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,75%,75%);stroke-opacity:1;stroke-miterlimit:10;" d="M 355.87738 731.805874 L 385.135193 742.77853 L 403.420349 720.837124 L 418.049255 764.723843 L 454.619568 750.094937 L 450.963318 793.981655 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 339.007812 65.871094 C 339.007812 62.539062 334.007812 62.539062 334.007812 65.871094 C 334.007812 69.207031 339.007812 69.207031 339.007812 65.871094 Z M 339.007812 65.871094 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 339.507812 65.871094 C 339.507812 61.871094 333.507812 61.871094 333.507812 65.871094 C 333.507812 69.871094 339.507812 69.871094 339.507812 65.871094 Z M 338.507812 65.871094 C 338.507812 63.207031 334.507812 63.207031 334.507812 65.871094 C 334.507812 68.539062 338.507812 68.539062 338.507812 65.871094 Z M 338.507812 65.871094 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(54.5%,71.8%,91.8%);fill-opacity:1;" d="M 357.292969 87.816406 C 357.292969 84.484375 352.292969 84.484375 352.292969 87.816406 C 352.292969 91.148438 357.292969 91.148438 357.292969 87.816406 Z M 357.292969 87.816406 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 357.792969 87.816406 C 357.792969 83.816406 351.792969 83.816406 351.792969 87.816406 C 351.792969 91.816406 357.792969 91.816406 357.792969 87.816406 Z M 356.792969 87.816406 C 356.792969 85.148438 352.792969 85.148438 352.792969 87.816406 C 352.792969 90.484375 356.792969 90.484375 356.792969 87.816406 Z M 356.792969 87.816406 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 371.921875 43.929688 C 371.921875 40.597656 366.921875 40.597656 366.921875 43.929688 C 366.921875 47.261719 371.921875 47.261719 371.921875 43.929688 Z M 371.921875 43.929688 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 372.421875 43.929688 C 372.421875 39.929688 366.421875 39.929688 366.421875 43.929688 C 366.421875 47.929688 372.421875 47.929688 372.421875 43.929688 Z M 371.421875 43.929688 C 371.421875 41.261719 367.421875 41.261719 367.421875 43.929688 C 367.421875 46.597656 371.421875 46.597656 371.421875 43.929688 Z M 371.421875 43.929688 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(93.3%,89%,45.1%);fill-opacity:1;" d="M 404.835938 14.671875 C 404.835938 11.339844 399.835938 11.339844 399.835938 14.671875 C 399.835938 18.003906 404.835938 18.003906 404.835938 14.671875 Z M 404.835938 14.671875 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 405.335938 14.671875 C 405.335938 10.671875 399.335938 10.671875 399.335938 14.671875 C 399.335938 18.671875 405.335938 18.671875 405.335938 14.671875 Z M 404.335938 14.671875 C 404.335938 12.003906 400.335938 12.003906 400.335938 14.671875 C 400.335938 17.339844 404.335938 17.339844 404.335938 14.671875 Z M 404.335938 14.671875 "/>
<path style="fill:none;stroke-width:2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 355.87738 731.805874 L 352.217224 706.204312 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 306.089844 102.445312 C 306.089844 99.109375 301.089844 99.109375 301.089844 102.445312 C 301.089844 105.777344 306.089844 105.777344 306.089844 102.445312 Z M 306.089844 102.445312 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 306.589844 102.445312 C 306.589844 98.445312 300.589844 98.445312 300.589844 102.445312 C 300.589844 106.445312 306.589844 106.445312 306.589844 102.445312 Z M 305.589844 102.445312 C 305.589844 99.777344 301.589844 99.777344 301.589844 102.445312 C 301.589844 105.109375 305.589844 105.109375 305.589844 102.445312 Z M 305.589844 102.445312 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(93.3%,89%,45.1%);fill-opacity:1;" d="M 309.75 76.84375 C 309.75 73.511719 304.75 73.511719 304.75 76.84375 C 304.75 80.175781 309.75 80.175781 309.75 76.84375 Z M 309.75 76.84375 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 310.25 76.84375 C 310.25 72.84375 304.25 72.84375 304.25 76.84375 C 304.25 80.84375 310.25 80.84375 310.25 76.84375 Z M 309.25 76.84375 C 309.25 74.175781 305.25 74.175781 305.25 76.84375 C 305.25 79.511719 309.25 79.511719 309.25 76.84375 Z M 309.25 76.84375 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(100%,100%,100%);fill-opacity:1;" d="M 408.492188 58.558594 C 408.492188 55.226562 403.492188 55.226562 403.492188 58.558594 C 403.492188 61.890625 408.492188 61.890625 408.492188 58.558594 Z M 408.492188 58.558594 "/>
<path style=" stroke:none;fill-rule:evenodd;fill:rgb(0%,0%,0%);fill-opacity:1;" d="M 408.992188 58.558594 C 408.992188 54.558594 402.992188 54.558594 402.992188 58.558594 C 402.992188 62.558594 408.992188 62.558594 408.992188 58.558594 Z M 407.992188 58.558594 C 407.992188 55.890625 403.992188 55.890625 403.992188 58.558594 C 403.992188 61.226562 407.992188 61.226562 407.992188 58.558594 Z M 407.992188 58.558594 "/>
<path style="fill:none;stroke-width:1.2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(100%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 138.861755 747.196499 L 148.416442 737.641812 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
<path style="fill:none;stroke-width:1.2;stroke-linecap:butt;stroke-linejoin:round;stroke:rgb(100%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 138.861755 737.641812 L 148.416442 747.196499 " transform="matrix(1,0,0,-1,-48.62738,808.649624)"/>
</g>
</svg>
