/// \defgroup PkgSurfaceMesher3Ref 3D Surface Mesh Generation Reference

/// \defgroup PkgSurfaceMesher3Concepts Concepts
/// \ingroup PkgSurfaceMesher3Ref


/// \defgroup PkgSurfaceMesher3Classes Mesh and Domain Classes
/// \ingroup PkgSurfaceMesher3Ref

/// \defgroup PkgSurfaceMesher3TagClasses Tag Classes
/// \ingroup PkgSurfaceMesher3Ref


/// \defgroup PkgSurfaceMesher3Functions Functions
/// \ingroup PkgSurfaceMesher3Ref


/// \defgroup PkgSurfaceMesher3FunctionsMakeMesh Mesh Generation
/// \ingroup PkgSurfaceMesher3Functions


/// \defgroup PkgSurfaceMesher3FunctionsIO I/O
/// \ingroup PkgSurfaceMesher3Functions


/*!
\addtogroup PkgSurfaceMesher3Ref
\todo check generated documentation
\cgalPkgDescriptionBegin{3D Surface Mesh Generation,PkgSurfaceMesher3}
\cgalPkgPicture{segmented_head-small.png}
\cgalPkgSummaryBegin
\cgalPkgAuthors{<PERSON> and Mariette <PERSON>}
\cgalPkgDesc{This package provides functions to generate surface  meshes that interpolate smooth surfaces. The meshing algorithm is based on Delaunay refinement and provides some guarantees on the resulting mesh: the user is able to control the size and shape of the mesh elements and  the accuracy of the surface approximation. There is no restriction on the topology and number of components of input surfaces. The surface mesh generator may also be used for non smooth surfaces but without guarantee.  Currently, implementations are provided for implicit surfaces described as the zero level set of some function and surfaces described as a gray level set in a three-dimensional image.}
\cgalPkgManuals{Chapter_3D_Surface_Mesh_Generation,PkgSurfaceMesher3Ref}
\cgalPkgSummaryEnd
\cgalPkgShortInfoBegin
\cgalPkgSince{3.2}
\cgalPkgDependsOn{\ref PkgTriangulation3}
\cgalPkgBib{cgal:ry-smg}
\cgalPkgLicense{\ref licensesGPL}
\cgalPkgShortInfoEnd
\cgalPkgDescriptionEnd

The surface mesh generation package offers a function template
which builds a triangular mesh approximating a surface.

The meshing algorithms requires to know the surface to be meshed
through an oracle that mainly can tell whether a
given segment, ray or line  intersects the surface or not
and can compute the compute the
intersections point if any. The oracle is represented by a traits class
which can be passed to the meshing function
or automatically generated for certain types of surfaces.
The current implementation provides
traits classes
to mesh implicit surfaces as well as  surfaces described
as a gray level in a three
dimensional image.

The output mesh conforms to some size and shape criteria
which are customizable. The criteria are passed to the mesher
through a parameter
whose type is a model of the
concept `SurfaceMeshFacetsCriteria_3`.

The meshing algorithm is a Delaunay refinement process
which is mainly guided by the criteria.
The output mesh may offer some guarantees, as being manifold,
homeomorphic to the surface or within a given
Hausdorff distance.  However, these guarantees depend
on the quality of the input surface (smoothness, with or without
boundary, manifold or  not),
the type and values of the given criteria.
The behavior of the refinement process can also be influenced through
a tag, which allows for instance to enforce the manifold property
of  the output mesh  while avoiding an over-refinement of the mesh.

\cgalClassifedRefPages

\cgalCRPSection{Concepts}
- `SurfaceMeshComplex_2InTriangulation_3`
- `SurfaceMeshTraits_3`
- `SurfaceMeshFacetsCriteria_3`
- `SurfaceMeshCellBase_3`
- `SurfaceMeshVertexBase_3`
- `SurfaceMeshTriangulation_3`
- `ImplicitFunction`
- `ImplicitSurfaceTraits_3`

\cgalCRPSection{Classes}
- `CGAL::Surface_mesh_complex_2_in_triangulation_3<Tr, Edge_info = void>`
- `CGAL::Surface_mesh_vertex_base_3<Gt,Vb>`
- `CGAL::Surface_mesh_cell_base_3<Gt,Cb>`
- `CGAL::Surface_mesh_default_criteria_3<Tr>`
- `CGAL::Surface_mesh_default_triangulation_3`
- `CGAL::Surface_mesh_traits_generator_3<Surface>`
- `CGAL::Implicit_surface_3<Traits, Function>`
- `CGAL::Gray_level_image_3<FT, Point>`

\cgalCRPSection{Tag Classes}
- `CGAL::Manifold_tag`
- `CGAL::Manifold_with_boundary_tag`
- `CGAL::Non_manifold_tag`

\cgalCRPSection{Function Templates}
- `CGAL::make_surface_mesh()`
- `CGAL::output_surface_facets_to_polyhedron()`
- `CGAL::facets_in_complex_2_to_triangle_mesh()`

*/

