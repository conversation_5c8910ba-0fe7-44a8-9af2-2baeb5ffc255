<?xml version="1.0" encoding="utf-8"  standalone="no"?>
<svg 
 width="600" height="480"
 viewBox="0 0 600 480"
 xmlns="http://www.w3.org/2000/svg"
 xmlns:xlink="http://www.w3.org/1999/xlink"
>

<title>Gnuplot</title>
<desc>Produced by GNUPLOT 5.2 patchlevel 2 </desc>

<g id="gnuplot_canvas">

<rect x="0" y="0" width="600" height="480" fill="none"/>
<defs>

	<circle id='gpDot' r='0.5' stroke-width='0.5'/>
	<path id='gpPt0' stroke-width='0.222' stroke='currentColor' d='M-1,0 h2 M0,-1 v2'/>
	<path id='gpPt1' stroke-width='0.222' stroke='currentColor' d='M-1,-1 L1,1 M1,-1 L-1,1'/>
	<path id='gpPt2' stroke-width='0.222' stroke='currentColor' d='M-1,0 L1,0 M0,-1 L0,1 M-1,-1 L1,1 M-1,1 L1,-1'/>
	<rect id='gpPt3' stroke-width='0.222' stroke='currentColor' x='-1' y='-1' width='2' height='2'/>
	<rect id='gpPt4' stroke-width='0.222' stroke='currentColor' fill='currentColor' x='-1' y='-1' width='2' height='2'/>
	<circle id='gpPt5' stroke-width='0.222' stroke='currentColor' cx='0' cy='0' r='1'/>
	<use xlink:href='#gpPt5' id='gpPt6' fill='currentColor' stroke='none'/>
	<path id='gpPt7' stroke-width='0.222' stroke='currentColor' d='M0,-1.33 L-1.33,0.67 L1.33,0.67 z'/>
	<use xlink:href='#gpPt7' id='gpPt8' fill='currentColor' stroke='none'/>
	<use xlink:href='#gpPt7' id='gpPt9' stroke='currentColor' transform='rotate(180)'/>
	<use xlink:href='#gpPt9' id='gpPt10' fill='currentColor' stroke='none'/>
	<use xlink:href='#gpPt3' id='gpPt11' stroke='currentColor' transform='rotate(45)'/>
	<use xlink:href='#gpPt11' id='gpPt12' fill='currentColor' stroke='none'/>
	<path id='gpPt13' stroke-width='0.222' stroke='currentColor' d='M0,1.330 L1.265,0.411 L0.782,-1.067 L-0.782,-1.076 L-1.265,0.411 z'/>
	<use xlink:href='#gpPt13' id='gpPt14' fill='currentColor' stroke='none'/>
	<filter id='textbox' filterUnits='objectBoundingBox' x='0' y='0' height='1' width='1'>
	  <feFlood flood-color='white' flood-opacity='1' result='bgnd'/>
	  <feComposite in='SourceGraphic' in2='bgnd' operator='atop'/>
	</filter>
	<filter id='greybox' filterUnits='objectBoundingBox' x='0' y='0' height='1' width='1'>
	  <feFlood flood-color='lightgrey' flood-opacity='1' result='grey'/>
	  <feComposite in='SourceGraphic' in2='grey' operator='atop'/>
	</filter>
</defs>
<g fill="none" color="white" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,422.4 L72.6,422.4 M552.8,422.4 L543.8,422.4  '/>	<g transform="translate(55.3,426.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 0</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,382.0 L72.6,382.0 M552.8,382.0 L543.8,382.0  '/>	<g transform="translate(55.3,385.9)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 5</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,341.5 L72.6,341.5 M552.8,341.5 L543.8,341.5  '/>	<g transform="translate(55.3,345.4)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 10</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,301.1 L72.6,301.1 M552.8,301.1 L543.8,301.1  '/>	<g transform="translate(55.3,305.0)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 15</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,260.7 L72.6,260.7 M552.8,260.7 L543.8,260.7  '/>	<g transform="translate(55.3,264.6)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 20</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,220.2 L72.6,220.2 M552.8,220.2 L543.8,220.2  '/>	<g transform="translate(55.3,224.1)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 25</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,179.8 L72.6,179.8 M552.8,179.8 L543.8,179.8  '/>	<g transform="translate(55.3,183.7)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 30</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,139.4 L72.6,139.4 M552.8,139.4 L543.8,139.4  '/>	<g transform="translate(55.3,143.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 35</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,99.0 L72.6,99.0 M552.8,99.0 L543.8,99.0  '/>	<g transform="translate(55.3,102.9)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 40</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,58.5 L72.6,58.5 M552.8,58.5 L543.8,58.5  '/>	<g transform="translate(55.3,62.4)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 45</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,18.1 L72.6,18.1 M552.8,18.1 L543.8,18.1  '/>	<g transform="translate(55.3,22.0)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 50</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M185.3,422.4 L185.3,413.4 M185.3,18.1 L185.3,27.1  '/>	<g transform="translate(185.3,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >5,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M307.0,422.4 L307.0,413.4 M307.0,18.1 L307.0,27.1  '/>	<g transform="translate(307.0,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >10,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M428.7,422.4 L428.7,413.4 M428.7,18.1 L428.7,27.1  '/>	<g transform="translate(428.7,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >15,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M550.4,422.4 L550.4,413.4 M550.4,18.1 L550.4,27.1  '/>	<g transform="translate(550.4,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >20,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,18.1 L63.6,422.4 L552.8,422.4 L552.8,18.1 L63.6,18.1 Z  '/></g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<g transform="translate(16.3,220.3) rotate(270)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >Time (sec)</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<g transform="translate(308.2,471.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >Path lengths</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
	<g id="gnuplot_plot_1" ><title>Homotopy test</title>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<g transform="translate(179.8,40.0)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" >Homotopy test</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<use xlink:href='#gpPt0' transform='translate(137.9,359.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(114.7,377.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(185.8,320.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(172.1,337.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(112.9,383.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(128.6,370.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(237.0,292.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(182.4,329.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(156.9,344.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(187.9,320.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.9,419.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(87.7,401.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(91.7,399.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.6,420.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.5,421.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(548.0,46.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(118.3,383.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(134.7,367.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(128.4,365.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(69.5,417.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(114.8,379.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(382.0,180.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(82.2,408.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.8,420.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(253.6,276.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(90.9,399.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(288.7,229.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(191.6,314.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(78.8,409.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.2,420.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(313.4,231.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,421.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.0,418.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(79.5,408.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(163.4,336.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(206.3,304.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(175.0,334.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.7,421.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.4,421.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,421.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(119.0,375.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(106.9,387.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(73.0,415.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(102.0,390.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.2,420.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(71.9,414.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(143.3,356.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(101.9,387.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(212.7,301.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(105.2,391.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(108.4,386.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(164.2,349.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(136.7,365.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.2,421.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(134.8,365.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(254.4,263.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(71.3,415.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,422.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(443.0,128.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(210.6,305.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(129.2,371.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(74.4,414.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.7,421.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(140.1,362.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(119.7,371.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.0,422.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(102.5,390.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(215.5,302.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(80.5,406.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(319.4,225.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(84.5,404.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(113.7,382.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(193.5,324.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.8,420.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(141.0,358.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(178.5,328.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(97.7,392.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(136.6,362.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(67.2,418.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(219.8,299.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(70.4,416.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(72.8,414.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.2,421.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(76.3,411.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(72.8,413.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(144.3,356.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.8,421.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,421.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(74.9,413.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(77.3,411.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(133.0,367.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(394.8,170.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(275.0,247.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,422.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(87.5,404.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(73.2,414.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(70.4,417.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(67.2,419.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(120.3,381.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.4,418.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.3,418.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(142.6,363.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(71.9,416.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.3,420.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(79.6,410.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(70.9,417.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(92.5,400.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(70.7,416.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.1,420.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(101.4,395.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(73.8,413.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.4,421.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(71.0,416.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.5,421.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(80.3,409.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(69.1,417.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(77.6,412.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.3,421.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(76.4,412.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.7,421.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(71.4,416.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.6,417.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.6,421.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(94.6,398.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.0,421.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.2,421.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.1,421.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.3,421.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.4,420.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(70.1,417.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.4,421.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(122.1,379.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.9,420.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.0,420.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(128.4,371.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(83.0,407.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.2,422.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.7,420.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(98.5,395.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.5,421.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.6,421.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(84.3,406.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(96.9,397.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.5,421.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(67.0,419.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(69.9,417.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(72.3,415.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(74.3,413.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.3,421.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.0,421.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(77.7,411.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.4,421.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.9,420.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.6,420.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.4,420.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.1,421.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.8,421.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.5,420.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.7,421.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.4,421.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(67.8,419.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.6,421.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.6,420.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(69.1,417.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(76.9,412.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(70.4,417.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.9,420.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.6,421.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.6,421.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(83.3,408.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(67.1,419.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(74.6,413.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(190.3,334.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(88.9,403.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(114.4,385.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.9,418.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(70.6,416.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.0,422.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(85.5,406.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(74.4,413.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(81.7,408.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.5,421.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.5,421.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.6,421.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.2,422.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.9,420.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.4,421.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(67.3,419.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(70.4,417.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.1,421.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(99.7,394.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.4,421.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(209.2,36.1) scale(4.50)' color='rgb(148,   0, 211)'/>
</g>
	</g>
	<g id="gnuplot_plot_2" ><title>Model Fit</title>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<g transform="translate(179.8,58.0)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" >Model Fit</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='rgb(  0, 158, 115)'  d='M188.1,54.1 L230.3,54.1 M63.6,422.0 L68.5,418.1 L73.5,414.2 L78.4,410.4 L83.4,406.5 L88.3,402.6
		L93.2,398.8 L98.2,394.9 L103.1,391.0 L108.1,387.2 L113.0,383.3 L118.0,379.4 L122.9,375.6 L127.8,371.7
		L132.8,367.8 L137.7,364.0 L142.7,360.1 L147.6,356.2 L152.5,352.4 L157.5,348.5 L162.4,344.6 L167.4,340.7
		L172.3,336.9 L177.3,333.0 L182.2,329.1 L187.1,325.3 L192.1,321.4 L197.0,317.5 L202.0,313.7 L206.9,309.8
		L211.8,305.9 L216.8,302.1 L221.7,298.2 L226.7,294.3 L231.6,290.5 L236.5,286.6 L241.5,282.7 L246.4,278.9
		L251.4,275.0 L256.3,271.1 L261.3,267.3 L266.2,263.4 L271.1,259.5 L276.1,255.7 L281.0,251.8 L286.0,247.9
		L290.9,244.1 L295.8,240.2 L300.8,236.3 L305.7,232.5 L310.7,228.6 L315.6,224.7 L320.6,220.9 L325.5,217.0
		L330.4,213.1 L335.4,209.3 L340.3,205.4 L345.3,201.5 L350.2,197.6 L355.1,193.8 L360.1,189.9 L365.0,186.0
		L370.0,182.2 L374.9,178.3 L379.9,174.4 L384.8,170.6 L389.7,166.7 L394.7,162.8 L399.6,159.0 L404.6,155.1
		L409.5,151.2 L414.4,147.4 L419.4,143.5 L424.3,139.6 L429.3,135.8 L434.2,131.9 L439.1,128.0 L444.1,124.2
		L449.0,120.3 L454.0,116.4 L458.9,112.6 L463.9,108.7 L468.8,104.8 L473.7,101.0 L478.7,97.1 L483.6,93.2
		L488.6,89.4 L493.5,85.5 L498.4,81.6 L503.4,77.8 L508.3,73.9 L513.3,70.0 L518.2,66.1 L523.2,62.3
		L528.1,58.4 L533.0,54.5 L538.0,50.7 L542.9,46.8 L547.9,42.9 L552.8,39.1  '/></g>
	</g>
<g fill="none" color="white" stroke="rgb(  0, 158, 115)" stroke-width="4.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="4.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="black" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,18.1 L63.6,422.4 L552.8,422.4 L552.8,18.1 L63.6,18.1 Z  '/></g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
</g>
</svg>

