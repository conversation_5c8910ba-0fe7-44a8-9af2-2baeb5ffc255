<?xml version="1.0" encoding="utf-8"  standalone="no"?>
<svg 
 width="600" height="480"
 viewBox="0 0 600 480"
 xmlns="http://www.w3.org/2000/svg"
 xmlns:xlink="http://www.w3.org/1999/xlink"
>

<title>Gnuplot</title>
<desc>Produced by GNUPLOT 5.2 patchlevel 2 </desc>

<g id="gnuplot_canvas">

<rect x="0" y="0" width="600" height="480" fill="none"/>
<defs>

	<circle id='gpDot' r='0.5' stroke-width='0.5'/>
	<path id='gpPt0' stroke-width='0.222' stroke='currentColor' d='M-1,0 h2 M0,-1 v2'/>
	<path id='gpPt1' stroke-width='0.222' stroke='currentColor' d='M-1,-1 L1,1 M1,-1 L-1,1'/>
	<path id='gpPt2' stroke-width='0.222' stroke='currentColor' d='M-1,0 L1,0 M0,-1 L0,1 M-1,-1 L1,1 M-1,1 L1,-1'/>
	<rect id='gpPt3' stroke-width='0.222' stroke='currentColor' x='-1' y='-1' width='2' height='2'/>
	<rect id='gpPt4' stroke-width='0.222' stroke='currentColor' fill='currentColor' x='-1' y='-1' width='2' height='2'/>
	<circle id='gpPt5' stroke-width='0.222' stroke='currentColor' cx='0' cy='0' r='1'/>
	<use xlink:href='#gpPt5' id='gpPt6' fill='currentColor' stroke='none'/>
	<path id='gpPt7' stroke-width='0.222' stroke='currentColor' d='M0,-1.33 L-1.33,0.67 L1.33,0.67 z'/>
	<use xlink:href='#gpPt7' id='gpPt8' fill='currentColor' stroke='none'/>
	<use xlink:href='#gpPt7' id='gpPt9' stroke='currentColor' transform='rotate(180)'/>
	<use xlink:href='#gpPt9' id='gpPt10' fill='currentColor' stroke='none'/>
	<use xlink:href='#gpPt3' id='gpPt11' stroke='currentColor' transform='rotate(45)'/>
	<use xlink:href='#gpPt11' id='gpPt12' fill='currentColor' stroke='none'/>
	<path id='gpPt13' stroke-width='0.222' stroke='currentColor' d='M0,1.330 L1.265,0.411 L0.782,-1.067 L-0.782,-1.076 L-1.265,0.411 z'/>
	<use xlink:href='#gpPt13' id='gpPt14' fill='currentColor' stroke='none'/>
	<filter id='textbox' filterUnits='objectBoundingBox' x='0' y='0' height='1' width='1'>
	  <feFlood flood-color='white' flood-opacity='1' result='bgnd'/>
	  <feComposite in='SourceGraphic' in2='bgnd' operator='atop'/>
	</filter>
	<filter id='greybox' filterUnits='objectBoundingBox' x='0' y='0' height='1' width='1'>
	  <feFlood flood-color='lightgrey' flood-opacity='1' result='grey'/>
	  <feComposite in='SourceGraphic' in2='grey' operator='atop'/>
	</filter>
</defs>
<g fill="none" color="white" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,422.4 L72.6,422.4 M567.0,422.4 L558.0,422.4  '/>	<g transform="translate(55.3,426.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 0</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,355.0 L72.6,355.0 M567.0,355.0 L558.0,355.0  '/>	<g transform="translate(55.3,358.9)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 10</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,287.6 L72.6,287.6 M567.0,287.6 L558.0,287.6  '/>	<g transform="translate(55.3,291.5)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 20</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,220.2 L72.6,220.2 M567.0,220.2 L558.0,220.2  '/>	<g transform="translate(55.3,224.1)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 30</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,152.9 L72.6,152.9 M567.0,152.9 L558.0,152.9  '/>	<g transform="translate(55.3,156.8)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 40</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,85.5 L72.6,85.5 M567.0,85.5 L558.0,85.5  '/>	<g transform="translate(55.3,89.4)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 50</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,18.1 L72.6,18.1 M567.0,18.1 L558.0,18.1  '/>	<g transform="translate(55.3,22.0)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" > 60</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M144.8,422.4 L144.8,413.4 M144.8,18.1 L144.8,27.1  '/>	<g transform="translate(144.8,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >10,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M226.0,422.4 L226.0,413.4 M226.0,18.1 L226.0,27.1  '/>	<g transform="translate(226.0,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >20,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M307.2,422.4 L307.2,413.4 M307.2,18.1 L307.2,27.1  '/>	<g transform="translate(307.2,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >30,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M388.4,422.4 L388.4,413.4 M388.4,18.1 L388.4,27.1  '/>	<g transform="translate(388.4,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >40,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M469.6,422.4 L469.6,413.4 M469.6,18.1 L469.6,27.1  '/>	<g transform="translate(469.6,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >50,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M550.8,422.4 L550.8,413.4 M550.8,18.1 L550.8,27.1  '/>	<g transform="translate(550.8,444.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >60,000,000</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,18.1 L63.6,422.4 L567.0,422.4 L567.0,18.1 L63.6,18.1 Z  '/></g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<g transform="translate(16.3,220.3) rotate(270)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >Time (sec)</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<g transform="translate(315.3,471.3)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="middle">
		<text><tspan font-family="Verdana" >Path lengths</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
	<g id="gnuplot_plot_1" ><title>Homotopy test</title>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<g transform="translate(179.8,40.0)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" >Homotopy test</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.6,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.7,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.7,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.7,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.7,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.7,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.7,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.7,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.7,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.7,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.7,422.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.8,422.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(63.9,422.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,422.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,422.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,422.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,422.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,422.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,422.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,422.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,422.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,422.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(64.1,422.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.2,421.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.2,421.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.2,421.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.2,421.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.2,421.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.2,421.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.2,421.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.2,421.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.2,421.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(65.2,421.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.9,420.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.9,421.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.9,420.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.9,420.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.9,420.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.9,420.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.9,420.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.9,419.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.9,419.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(66.9,420.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.5,418.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.5,418.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.5,418.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.5,418.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.5,418.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.5,418.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.5,418.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.5,418.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.5,418.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(68.5,418.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(79.9,410.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(79.9,410.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(79.9,410.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(79.9,410.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(79.9,410.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(79.9,410.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(79.9,411.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(79.9,410.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(79.9,411.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(79.9,410.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(96.1,400.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(96.1,400.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(96.1,399.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(96.1,399.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(96.1,398.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(96.1,398.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(96.1,399.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(96.1,398.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(96.1,399.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(96.1,399.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(112.3,387.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(112.3,386.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(112.3,386.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(112.3,385.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(112.3,384.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(112.3,385.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(112.3,385.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(112.3,385.9) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(112.3,387.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(112.3,389.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(226.0,302.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(226.0,307.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(226.0,306.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(226.0,311.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(226.0,303.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(226.0,311.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(226.0,302.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(226.0,313.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(226.0,305.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(226.0,313.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(388.4,198.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(388.4,201.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(388.4,202.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(388.4,201.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(388.4,196.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(388.4,197.3) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(388.4,194.2) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(388.4,201.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(388.4,202.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(388.4,198.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(550.8,77.4) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(550.8,84.6) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(550.8,77.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(550.8,75.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(550.8,89.1) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(550.8,82.5) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(550.8,77.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(550.8,72.8) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(550.8,76.0) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(550.8,76.7) scale(4.50)' color='rgb(148,   0, 211)'/>
	<use xlink:href='#gpPt0' transform='translate(209.2,36.1) scale(4.50)' color='rgb(148,   0, 211)'/>
</g>
	</g>
	<g id="gnuplot_plot_2" ><title>Model Fit</title>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<g transform="translate(179.8,58.0)" stroke="none" fill="black" font-family="Verdana" font-size="12.00"  text-anchor="end">
		<text><tspan font-family="Verdana" >Model Fit</tspan></text>
	</g>
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='rgb(  0, 158, 115)'  d='M188.1,54.1 L230.3,54.1 M63.6,422.3 L68.7,418.8 L73.8,415.2 L78.9,411.6 L83.9,408.1 L89.0,404.5
		L94.1,401.0 L99.2,397.4 L104.3,393.9 L109.4,390.3 L114.4,386.7 L119.5,383.2 L124.6,379.6 L129.7,376.1
		L134.8,372.5 L139.9,368.9 L145.0,365.4 L150.0,361.8 L155.1,358.3 L160.2,354.7 L165.3,351.2 L170.4,347.6
		L175.5,344.0 L180.6,340.5 L185.6,336.9 L190.7,333.4 L195.8,329.8 L200.9,326.3 L206.0,322.7 L211.1,319.1
		L216.1,315.6 L221.2,312.0 L226.3,308.5 L231.4,304.9 L236.5,301.3 L241.6,297.8 L246.7,294.2 L251.7,290.7
		L256.8,287.1 L261.9,283.6 L267.0,280.0 L272.1,276.4 L277.2,272.9 L282.2,269.3 L287.3,265.8 L292.4,262.2
		L297.5,258.6 L302.6,255.1 L307.7,251.5 L312.8,248.0 L317.8,244.4 L322.9,240.9 L328.0,237.3 L333.1,233.7
		L338.2,230.2 L343.3,226.6 L348.4,223.1 L353.4,219.5 L358.5,215.9 L363.6,212.4 L368.7,208.8 L373.8,205.3
		L378.9,201.7 L383.9,198.2 L389.0,194.6 L394.1,191.0 L399.2,187.5 L404.3,183.9 L409.4,180.4 L414.5,176.8
		L419.5,173.2 L424.6,169.7 L429.7,166.1 L434.8,162.6 L439.9,159.0 L445.0,155.5 L450.0,151.9 L455.1,148.3
		L460.2,144.8 L465.3,141.2 L470.4,137.7 L475.5,134.1 L480.6,130.5 L485.6,127.0 L490.7,123.4 L495.8,119.9
		L500.9,116.3 L506.0,112.8 L511.1,109.2 L516.2,105.6 L521.2,102.1 L526.3,98.5 L531.4,95.0 L536.5,91.4
		L541.6,87.8 L546.7,84.3 L551.7,80.7 L556.8,77.2 L561.9,73.6 L567.0,70.1  '/></g>
	</g>
<g fill="none" color="white" stroke="rgb(  0, 158, 115)" stroke-width="4.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="4.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="black" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
	<path stroke='black'  d='M63.6,18.1 L63.6,422.4 L567.0,422.4 L567.0,18.1 L63.6,18.1 Z  '/></g>
<g fill="none" color="black" stroke="currentColor" stroke-width="2.00" stroke-linecap="butt" stroke-linejoin="miter">
</g>
</g>
</svg>

