Given two walks in the vertex-edge graph of a combinatorial map, this package provides linear time algorithms to decide if the walks are homotopic, i.e. can be continuously deformed one into the other on the surface of the combinatorial map. Two notions of homotopy are proposed. Homotopy with fixed basepoints applies to non necessarily closed walks and assumes that the common endpoints of the walks stay fix during the deformation. Free homotopy applies only to closed walks and does not impose any restriction on the deformation. Another helpful algorithm is provided to test if a single curve is contractible; it is equivalent to a homotopy test where one of the closed walks is reduced to a  point.

This package also provides a linear time algorithm to find a shortest non-contractible cycle through a vertex and a quadratic time algorithm to find the edge-width or the face-width of the mesh. If the geometric distances between vertices are considered, the complexity of the algorithms is raised by a logarithmic factor.