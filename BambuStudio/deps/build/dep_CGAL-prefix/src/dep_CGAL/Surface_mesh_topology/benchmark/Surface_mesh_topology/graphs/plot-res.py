import matplotlib.pyplot as plt
import numpy as np
# exec(open("./plot-res.py").read())

x = [10, 10, 10, 20, 20, 20, 30, 30, 30, 100, 100, 100, 200, 200, 200, 300, 300, 300, 1000, 1000, 1000, 2000, 2000, 2000, 3000, 3000, 3000, 10000, 10000, 10000, 20000, 20000, 20000, 30000, 30000, 30000, 100000, 100000, 100000, 200000, 200000, 200000, 300000, 300000, 300000, 1000000, 1000000, 1000000, 2000000, 2000000, 2000000, 3000000, 3000000, 3000000, 10000000, 10000000, 10000000, 20000000, 20000000, 20000000, 30000000, 30000000, 30000000]

y_nfh = [3e-05, 2.8e-05, 4.1e-05,
5e-05, 3.1e-05, 3.3e-05,
4.8e-05, 3.9e-05, 4.2e-05, 
0, 0.000141, 0.000107,
0.000285, 0.000289, 0.000213,
0.000349, 0.000333, 0.000334, 
0, 0.001847, 0.001052,
0.002631, 0.001751, 0.002106,
0.003527, 0.003165, 0.003182,
0.010599, 0.010524, 0.010555,
0.021093, 0.020575, 0.020579,
0.032026, 0.032009, 0.032175,
0.10946, 0.108055, 0.107969,
0.219881, 0.22562, 0.220325,
0.337536, 0.329134, 0.333249,
1.10841, 1.16317, 1.14465,
2.35853, 2.2348, 2.33958,
3.53096, 3.4077, 3.30577,
10.9284, 11.0523, 11.0834,
22.0406, 22.0634, 22.368,
33.8717, 34.2746, 33.8732]


col_pts = '#9400d3'
col_line = '#009e73'
# log-log plot
#plt.plot(x[0:63], y_nfh[0:63], 'b+')
plt.plot(x[0:63], y_nfh[0:63], linestyle = 'None', color = col_pts, marker='+')
# plt.plot(x[0:63], y_fh[0:63], 'ro')
# plt.plot(x[0:63], y_c[0:63], 'g+')
plt.xscale('log')
plt.yscale('log')
plt.xlabel('log(path length)')
plt.ylabel('log(time in sec.)')
plt.plot([x[0],x[62]],[1.5e-05,y_nfh[62]], color = col_line)
# plt.plot([x[0],x[62]],[y_fh[0],y_fh[62]], 'r')
# plt.plot([x[0],x[62]],[y_c[0],y_c[62]], 'g')
# plt.title('Random paths on the canonical reduced genus 5 surface')
plt.grid(False)
plt.show()
