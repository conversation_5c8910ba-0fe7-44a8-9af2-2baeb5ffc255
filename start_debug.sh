#!/bin/bash

# BambuStudio 快速调试启动脚本
# Quick Debug Startup Script for BambuStudio

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 BambuStudio 调试环境快速启动${NC}"
echo "=================================================="

# 检查VSCode是否安装
if ! command -v code &> /dev/null; then
    echo -e "${YELLOW}⚠️  VSCode 未安装或未添加到PATH${NC}"
    echo "请安装VSCode并确保'code'命令可用"
    exit 1
fi

# 检查项目状态
echo -e "${BLUE}📋 检查项目状态...${NC}"
cd "$SCRIPT_DIR/BambuStudio"
./debug_helper.sh status

echo ""
echo -e "${BLUE}🔧 可用的调试选项:${NC}"
echo "1. 在VSCode中打开项目进行图形化调试"
echo "2. 使用命令行LLDB调试"
echo "3. 运行BambuStudio（不调试）"
echo "4. 编译项目"
echo "5. 查看调试指南"
echo ""

read -p "请选择选项 (1-5): " choice

case $choice in
    1)
        echo -e "${GREEN}🎯 在VSCode中打开项目...${NC}"
        code "$SCRIPT_DIR/BambuStudio.code-workspace"
        echo ""
        echo -e "${BLUE}📖 VSCode调试步骤:${NC}"
        echo "1. 等待VSCode加载完成"
        echo "2. 安装推荐的扩展（如果提示）"
        echo "3. 在代码中设置断点（点击行号左侧）"
        echo "4. 按F5开始调试，或点击调试面板的'开始调试'"
        echo "5. 选择调试配置（推荐：Debug BambuStudio）"
        echo ""
        echo -e "${YELLOW}💡 提示: 查看 DEBUG_GUIDE.md 获取详细调试指南${NC}"
        ;;
    2)
        echo -e "${GREEN}🐛 启动LLDB调试...${NC}"
        ./debug_helper.sh debug
        ;;
    3)
        echo -e "${GREEN}▶️  运行BambuStudio...${NC}"
        ./debug_helper.sh run
        ;;
    4)
        echo -e "${GREEN}🔨 编译项目...${NC}"
        ./debug_helper.sh build
        ;;
    5)
        echo -e "${GREEN}📚 打开调试指南...${NC}"
        if command -v open &> /dev/null; then
            open "BambuStudio/DEBUG_GUIDE.md"
        else
            echo "请查看文件: BambuStudio/DEBUG_GUIDE.md"
        fi
        ;;
    *)
        echo -e "${YELLOW}❌ 无效选项${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${GREEN}✅ 完成！${NC}"
